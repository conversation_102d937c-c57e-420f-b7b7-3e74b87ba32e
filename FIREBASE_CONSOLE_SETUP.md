# دليل إعداد Firebase Console خطوة بخطوة

## 🚀 الخطوة 1: الوصول إلى Firebase Console

1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. سجل دخول بحساب Google
3. اختر مشروع `legal2025`

## 🔐 الخطوة 2: تفعيل Firebase Authentication

### أ. الوصول إلى Authentication
1. في القائمة الجانبية، اضغط على **"Authentication"**
2. اضغط على **"Get started"** إذا لم يكن مُفعلاً

### ب. تفعيل Email/Password
1. اضغط على تبويب **"Sign-in method"**
2. ابحث عن **"Email/Password"**
3. اضغط على **"Email/Password"**
4. فعّل **"Enable"** (الخيار الأول)
5. فعّل **"Email link (passwordless sign-in)"** (اختياري)
6. اضغط **"Save"**

### ج. تفعيل Anonymous Authentication
1. في نفس الصفحة، ابحث عن **"Anonymous"**
2. اضغط على **"Anonymous"**
3. فعّل **"Enable"**
4. اضغط **"Save"**

## 🌐 الخطوة 3: إعداد Authorized Domains

### أ. الوصول إلى Settings
1. في صفحة Authentication، اضغط على تبويب **"Settings"**
2. انزل إلى قسم **"Authorized domains"**

### ب. إضافة النطاقات المطلوبة
اضغط **"Add domain"** وأضف النطاقات التالية:

```
localhost
127.0.0.1:8080
legal2025.firebaseapp.com
```

**ملاحظة**: `legal2025.firebaseapp.com` موجود بالفعل، تأكد من وجود الباقي.

## 🗄️ الخطوة 4: إعداد Firestore Database

### أ. إنشاء Database
1. في القائمة الجانبية، اضغط على **"Firestore Database"**
2. اضغط **"Create database"**
3. اختر **"Start in test mode"** (مؤقتاً)
4. اختر الموقع الجغرافي (مثل `europe-west1`)
5. اضغط **"Done"**

### ب. إعداد Security Rules (مؤقت)
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

## 📁 الخطوة 5: إعداد Firebase Storage

### أ. إنشاء Storage
1. في القائمة الجانبية، اضغط على **"Storage"**
2. اضغط **"Get started"**
3. اختر **"Start in test mode"**
4. اختر نفس الموقع الجغرافي
5. اضغط **"Done"**

### ب. إعداد Storage Rules (مؤقت)
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if true;
    }
  }
}
```

## 🔔 الخطوة 6: إعداد Cloud Messaging (اختياري)

1. في القائمة الجانبية، اضغط على **"Cloud Messaging"**
2. اضغط **"Get started"**
3. لا حاجة لإعدادات إضافية الآن

## ⚙️ الخطوة 7: التحقق من Project Settings

### أ. فحص General Settings
1. اضغط على أيقونة الترس ⚙️ > **"Project settings"**
2. في تبويب **"General"**:
   - تأكد من **Project ID**: `legal2025`
   - تأكد من **Project name**: `legal2025`

### ب. فحص Web App Configuration
1. انزل إلى قسم **"Your apps"**
2. تأكد من وجود Web App
3. إذا لم يكن موجوداً، اضغط **"Add app"** > **"Web"**
4. أدخل اسم التطبيق: `legal2025-web`
5. فعّل **"Firebase Hosting"** (اختياري)

## 🧪 الخطوة 8: اختبار الإعدادات

### أ. في التطبيق
1. شغّل التطبيق: `flutter run -d chrome`
2. اذهب لصفحة تسجيل الدخول
3. اضغط **"🩺 تشخيص Firebase"**
4. تحقق من النتائج

### ب. اختبار تسجيل الدخول
1. جرب **"اختبار تسجيل الدخول المجهول"**
2. جرب **"اختبار إنشاء حساب بالإيميل"**
3. تحقق من ظهور المستخدمين في Authentication > Users

## ✅ قائمة التحقق النهائية

- [ ] تم تفعيل Email/Password في Authentication
- [ ] تم تفعيل Anonymous في Authentication  
- [ ] تم إضافة localhost و 127.0.0.1 في Authorized domains
- [ ] تم إنشاء Firestore Database في test mode
- [ ] تم إنشاء Storage في test mode
- [ ] تم التحقق من Project Settings
- [ ] تم اختبار التطبيق بنجاح

## 🚨 مشاكل شائعة وحلولها

### مشكلة: "operation-not-allowed"
**الحل**: تأكد من تفعيل Email/Password في Sign-in methods

### مشكلة: "auth domain not authorized"  
**الحل**: أضف localhost في Authorized domains

### مشكلة: "network error"
**الحل**: تحقق من الاتصال بالإنترنت والـ Firewall

### مشكلة: "quota exceeded"
**الحل**: تحقق من Usage في Project Overview

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من Firebase Status: [status.firebase.google.com](https://status.firebase.google.com)
2. راجع Firebase Documentation
3. استخدم أداة التشخيص في التطبيق
4. تحقق من Console logs في المتصفح

## ⚠️ ملاحظات أمنية مهمة

**للإنتاج فقط:**
- غيّر Security Rules لتكون أكثر تقييداً
- أزل test mode من Firestore و Storage  
- أضف authentication requirements للقراءة/الكتابة
- راجع Authorized domains وأزل غير المطلوب

---

**بعد اتباع هذه الخطوات، يجب أن يعمل Firebase Authentication بشكل مثالي! 🎉**
