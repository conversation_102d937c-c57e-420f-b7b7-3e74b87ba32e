@echo off
title تنظيف طوارئ - تحرير أقصى مساحة ممكنة
color 0C

echo.
echo ⚠️⚠️⚠️ تنظيف طوارئ - تحرير أقصى مساحة ممكنة ⚠️⚠️⚠️
echo =====================================================
echo.

echo 🛡️  المشاريع المحمية فقط:
echo   ✅ legal2025 (D:\20223\2025\legl92025)
echo   ✅ اذكاري
echo.

echo ❌❌❌ سيتم حذف كل شيء آخر تقريباً ❌❌❌
echo   ❌ جميع مشاريع التطوير الأخرى
echo   ❌ جميع ملفات التحميل
echo   ❌ جميع ملفات سطح المكتب (عدا المحمية)
echo   ❌ جميع ملفات المستندات (عدا المحمية)
echo   ❌ جميع cache وملفات مؤقتة
echo   ❌ جميع ملفات النظام غير الضرورية
echo   ❌ جميع البرامج المؤقتة
echo.

echo 📊 فحص مساحة القرص...
for /f "tokens=3" %%a in ('dir C:\ ^| find "bytes free"') do set BEFORE=%%a
echo المساحة المتاحة: %BEFORE% bytes
echo.

set /p confirm="⚠️  هذا تنظيف جذري! هل تريد المتابعة؟ (YES/NO): "
if /i not "%confirm%"=="YES" goto :end

echo.
echo 🚨 بدء التنظيف الجذري...
echo ========================
echo.

REM إيقاف جميع العمليات غير الضرورية
echo 1. إيقاف العمليات غير الضرورية...
taskkill /f /im chrome.exe 2>nul
taskkill /f /im msedge.exe 2>nul
taskkill /f /im firefox.exe 2>nul
taskkill /f /im code.exe 2>nul
taskkill /f /im studio64.exe 2>nul
taskkill /f /im idea64.exe 2>nul
echo   ✅ تم إيقاف البرامج

REM تنظيف جذري للملفات المؤقتة
echo 2. تنظيف جذري للملفات المؤقتة...
rd /s /q "%TEMP%" 2>nul
md "%TEMP%"
rd /s /q "C:\Windows\Temp" 2>nul
md "C:\Windows\Temp"
rd /s /q "C:\Windows\Prefetch" 2>nul
rd /s /q "C:\Windows\SoftwareDistribution\Download" 2>nul
md "C:\Windows\SoftwareDistribution\Download"
echo   ✅ تم تنظيف الملفات المؤقتة

REM تنظيف جذري لسلة المحذوفات
echo 3. تنظيف سلة المحذوفات...
rd /s /q "C:\$Recycle.Bin" 2>nul
echo   ✅ تم تنظيف سلة المحذوفات

REM حذف جميع ملفات التحميل
echo 4. حذف جميع ملفات التحميل...
if exist "C:\Users\<USER>\Downloads" (
    for %%i in ("C:\Users\<USER>\Downloads\*") do (
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            del /q /f "%%i" 2>nul
        )
    )
    for /d %%i in ("C:\Users\<USER>\Downloads\*") do (
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            rd /s /q "%%i" 2>nul
        )
    )
)
echo   ✅ تم حذف ملفات التحميل

REM حذف جميع مشاريع سطح المكتب (عدا المحمية)
echo 5. تنظيف سطح المكتب...
if exist "C:\Users\<USER>\Desktop" (
    for /d %%i in ("C:\Users\<USER>\Desktop\*") do (
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            rd /s /q "%%i" 2>nul
        )
    )
)
echo   ✅ تم تنظيف سطح المكتب

REM حذف جميع مشاريع المستندات (عدا المحمية)
echo 6. تنظيف مجلد المستندات...
if exist "C:\Users\<USER>\Documents" (
    for /d %%i in ("C:\Users\<USER>\Documents\*") do (
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            rd /s /q "%%i" 2>nul
        )
    )
)
echo   ✅ تم تنظيف المستندات

REM حذف جميع مشاريع Android Studio
echo 7. حذف جميع مشاريع Android Studio...
if exist "C:\Users\<USER>\AndroidStudioProjects" (
    for /d %%i in ("C:\Users\<USER>\AndroidStudioProjects\*") do (
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            rd /s /q "%%i" 2>nul
        )
    )
)
echo   ✅ تم حذف مشاريع Android Studio

REM تنظيف جذري لـ cache المتصفحات
echo 8. تنظيف جذري لـ cache المتصفحات...
rd /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache" 2>nul
rd /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Code Cache" 2>nul
rd /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\GPUCache" 2>nul
rd /s /q "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache" 2>nul
rd /s /q "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Code Cache" 2>nul
rd /s /q "%APPDATA%\Mozilla\Firefox\Profiles" 2>nul
echo   ✅ تم تنظيف cache المتصفحات

REM حذف جميع مجلدات node_modules
echo 9. حذف جميع مجلدات node_modules...
for /d /r C:\ %%i in (node_modules) do (
    if exist "%%i" (
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            rd /s /q "%%i" 2>nul
        )
    )
)
echo   ✅ تم حذف node_modules

REM حذف جميع مجلدات build
echo 10. حذف جميع مجلدات build...
for /d /r C:\ %%i in (build) do (
    if exist "%%i" (
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            rd /s /q "%%i" 2>nul
        )
    )
)
echo   ✅ تم حذف مجلدات build

REM تنظيف جذري لـ Gradle
echo 11. تنظيف Gradle...
rd /s /q "%USERPROFILE%\.gradle" 2>nul
echo   ✅ تم حذف Gradle cache

REM تنظيف جذري لـ Pub Cache
echo 12. تنظيف Pub Cache...
rd /s /q "%LOCALAPPDATA%\Pub\Cache" 2>nul
rd /s /q "%USERPROFILE%\.pub-cache" 2>nul
echo   ✅ تم حذف Pub Cache

REM حذف جميع ملفات APK/ISO/IMG
echo 13. حذف ملفات APK/ISO/IMG...
for /r C:\ %%i in (*.apk *.iso *.img *.dmg) do (
    if exist "%%i" (
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            del /q /f "%%i" 2>nul
        )
    )
)
echo   ✅ تم حذف ملفات APK/ISO/IMG

REM تنظيف Android SDK
echo 14. تنظيف Android SDK...
if exist "%LOCALAPPDATA%\Android\Sdk" (
    rd /s /q "%LOCALAPPDATA%\Android\Sdk\.temp" 2>nul
    rd /s /q "%LOCALAPPDATA%\Android\Sdk\build-cache" 2>nul
    rd /s /q "%LOCALAPPDATA%\Android\Sdk\platforms\android-28" 2>nul
    rd /s /q "%LOCALAPPDATA%\Android\Sdk\platforms\android-29" 2>nul
    rd /s /q "%LOCALAPPDATA%\Android\Sdk\platforms\android-30" 2>nul
    rd /s /q "%LOCALAPPDATA%\Android\Sdk\platforms\android-31" 2>nul
    rd /s /q "%LOCALAPPDATA%\Android\Sdk\platforms\android-32" 2>nul
)
echo   ✅ تم تنظيف Android SDK

REM تنظيف Visual Studio
echo 15. تنظيف Visual Studio...
for /d %%i in ("%LOCALAPPDATA%\Microsoft\VisualStudio*") do (
    if exist "%%i" (
        rd /s /q "%%i\ComponentModelCache" 2>nul
        rd /s /q "%%i\Extensions" 2>nul
        rd /s /q "%%i\log" 2>nul
        rd /s /q "%%i\tmp" 2>nul
    )
)
echo   ✅ تم تنظيف Visual Studio

REM تنظيف VS Code
echo 16. تنظيف VS Code...
rd /s /q "%APPDATA%\Code\User\workspaceStorage" 2>nul
rd /s /q "%APPDATA%\Code\logs" 2>nul
rd /s /q "%APPDATA%\Code\CachedExtensions" 2>nul
echo   ✅ تم تنظيف VS Code

REM حذف Event Logs
echo 17. حذف Event Logs...
for /f "tokens=*" %%i in ('wevtutil el 2^>nul') do (
    wevtutil cl "%%i" 2>nul
)
echo   ✅ تم حذف Event Logs

REM تنظيف IIS
echo 18. تنظيف IIS...
if exist "C:\inetpub\logs" (
    rd /s /q "C:\inetpub\logs" 2>nul
    md "C:\inetpub\logs"
)
echo   ✅ تم تنظيف IIS

REM حذف ملفات النظام المؤقتة
echo 19. حذف ملفات النظام المؤقتة...
del /q /f "C:\Windows\*.log" 2>nul
del /q /f "C:\Windows\*.tmp" 2>nul
del /q /f "C:\*.tmp" 2>nul
del /q /f "C:\*.temp" 2>nul
del /q /f "C:\Windows\Logs\*" 2>nul
echo   ✅ تم حذف ملفات النظام المؤقتة

REM تشغيل Disk Cleanup بقوة
echo 20. تشغيل Disk Cleanup...
cleanmgr /sagerun:1
echo   ✅ تم تشغيل Disk Cleanup

echo.
echo 📊 حساب النتائج...
for /f "tokens=3" %%a in ('dir C:\ ^| find "bytes free"') do set AFTER=%%a

echo.
echo ✅✅✅ تم الانتهاء من التنظيف الجذري! ✅✅✅
echo =============================================
echo.

echo 📊 النتائج:
echo   📈 المساحة قبل التنظيف: %BEFORE% bytes
echo   📈 المساحة بعد التنظيف: %AFTER% bytes
echo.

echo 🛡️  المشاريع المحمية (لم تُحذف):
echo   ✅ legal2025 (D:\20223\2025\legl92025)
echo   ✅ اذكاري (إذا وُجد)
echo.

echo 🎉 تم تحرير مساحة كبيرة جداً!
echo.

echo 💡 خطوات إضافية لتحرير المزيد:
echo   1. إعادة تشغيل الكمبيوتر فوراً
echo   2. تشغيل: chkdsk C: /f
echo   3. حذف البرامج غير المستخدمة من Control Panel
echo   4. نقل الملفات الكبيرة لقرص آخر
echo   5. استخدام أداة CCleaner
echo.

echo 🚀 الآن يجب أن تكون المساحة كافية لتشغيل legal2025!
echo.

:end
pause
