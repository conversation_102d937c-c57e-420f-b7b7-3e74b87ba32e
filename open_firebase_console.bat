@echo off
echo 🔥 فتح Firebase Console لمشروع legal2025...
echo.

echo 📋 الروابط المهمة:
echo.
echo 1. Firebase Console الرئيسي:
echo    https://console.firebase.google.com/project/legal2025
echo.
echo 2. Authentication:
echo    https://console.firebase.google.com/project/legal2025/authentication
echo.
echo 3. Firestore Database:
echo    https://console.firebase.google.com/project/legal2025/firestore
echo.
echo 4. Storage:
echo    https://console.firebase.google.com/project/legal2025/storage
echo.
echo 5. Project Settings:
echo    https://console.firebase.google.com/project/legal2025/settings/general
echo.

echo 🌐 فتح Firebase Console...
start https://console.firebase.google.com/project/legal2025

echo.
echo 🧪 فتح أداة فحص Firebase المحلية...
start check_firebase.html

echo.
echo ✅ تم فتح Firebase Console وأداة الفحص!
echo.
echo 📝 خطوات مهمة:
echo    1. تأكد من تفعيل Email/Password في Authentication
echo    2. تأكد من تفعيل Anonymous في Authentication  
echo    3. أضف localhost في Authorized domains
echo    4. تأكد من إنشاء Firestore Database
echo.
pause
