@echo off
title التنظيف النهائي - تحرير أقصى مساحة ممكنة
color 0A

echo.
echo ██╗   ██╗██╗  ████████╗██╗███╗   ███╗ █████╗ ████████╗███████╗
echo ██║   ██║██║  ╚══██╔══╝██║████╗ ████║██╔══██╗╚══██╔══╝██╔════╝
echo ██║   ██║██║     ██║   ██║██╔████╔██║███████║   ██║   █████╗  
echo ██║   ██║██║     ██║   ██║██║╚██╔╝██║██╔══██║   ██║   ██╔══╝  
echo ╚██████╔╝███████╗██║   ██║██║ ╚═╝ ██║██║  ██║   ██║   ███████╗
echo  ╚═════╝ ╚══════╝╚═╝   ╚═╝╚═╝     ╚═╝╚═╝  ╚═╝   ╚═╝   ╚══════╝
echo.
echo 🚀 التنظيف النهائي - تحرير أقصى مساحة ممكنة
echo ==========================================
echo.

echo 🛡️  المشاريع المحمية:
echo   ✅ legal2025 (D:\20223\2025\legl92025)
echo   ✅ اذكاري (جميع المواقع)
echo.

echo 📊 فحص مساحة القرص الحالية...
for /f "tokens=3" %%a in ('dir C:\ ^| find "bytes free"') do set INITIAL=%%a
echo المساحة المتاحة حالياً: %INITIAL% bytes
echo.

echo ⚠️⚠️⚠️ تحذير: هذا تنظيف شامل وجذري! ⚠️⚠️⚠️
echo.
echo سيتم تنفيذ:
echo   🔍 1. فحص الملفات الكبيرة
echo   🧹 2. تنظيف طوارئ شامل
echo   🪟 3. تنظيف ملفات Windows النظام
echo   🦋 4. تنظيف Flutter خاص
echo   🔧 5. تحسينات إضافية
echo.

set /p confirm="هل تريد المتابعة مع التنظيف الشامل؟ (YES/NO): "
if /i not "%confirm%"=="YES" goto :end

echo.
echo 🚀 بدء التنظيف النهائي...
echo ========================
echo.

REM المرحلة 1: فحص الملفات الكبيرة
echo 🔍 المرحلة 1: فحص الملفات الكبيرة...
echo ===================================
call find_large_files.bat
echo ✅ انتهت المرحلة 1
echo.
pause

REM المرحلة 2: تنظيف طوارئ
echo 🧹 المرحلة 2: تنظيف طوارئ شامل...
echo ================================
call EMERGENCY_CLEANUP.bat
for /f "tokens=3" %%a in ('dir C:\ ^| find "bytes free"') do set AFTER_EMERGENCY=%%a
echo المساحة بعد التنظيف الطارئ: %AFTER_EMERGENCY% bytes
echo ✅ انتهت المرحلة 2
echo.
pause

REM المرحلة 3: تنظيف Windows النظام
echo 🪟 المرحلة 3: تنظيف ملفات Windows النظام...
echo ==========================================
call windows_system_cleanup.bat
for /f "tokens=3" %%a in ('dir C:\ ^| find "bytes free"') do set AFTER_WINDOWS=%%a
echo المساحة بعد تنظيف Windows: %AFTER_WINDOWS% bytes
echo ✅ انتهت المرحلة 3
echo.
pause

REM المرحلة 4: تنظيف Flutter
echo 🦋 المرحلة 4: تنظيف Flutter خاص...
echo ===============================
call flutter_cleanup.bat
for /f "tokens=3" %%a in ('dir C:\ ^| find "bytes free"') do set AFTER_FLUTTER=%%a
echo المساحة بعد تنظيف Flutter: %AFTER_FLUTTER% bytes
echo ✅ انتهت المرحلة 4
echo.

REM المرحلة 5: تحسينات إضافية
echo 🔧 المرحلة 5: تحسينات إضافية...
echo ==============================

echo   - إيقاف الخدمات غير الضرورية...
sc config "Fax" start= disabled 2>nul
sc config "TabletInputService" start= disabled 2>nul
sc config "WSearch" start= disabled 2>nul

echo   - تحسين إعدادات الذاكرة...
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "ClearPageFileAtShutdown" /t REG_DWORD /d 1 /f

echo   - تحسين إعدادات القرص...
fsutil behavior set DisableLastAccess 1

echo   - تنظيف DNS cache...
ipconfig /flushdns

echo   - تحسين Network settings...
netsh int tcp set global autotuninglevel=normal

echo ✅ انتهت المرحلة 5
echo.

REM حساب النتائج النهائية
for /f "tokens=3" %%a in ('dir C:\ ^| find "bytes free"') do set FINAL=%%a

echo.
echo ✅✅✅ تم الانتهاء من التنظيف النهائي! ✅✅✅
echo ==========================================
echo.

echo 📊 النتائج النهائية:
echo ==================
echo   📈 المساحة في البداية: %INITIAL% bytes
echo   📈 بعد التنظيف الطارئ: %AFTER_EMERGENCY% bytes  
echo   📈 بعد تنظيف Windows: %AFTER_WINDOWS% bytes
echo   📈 بعد تنظيف Flutter: %AFTER_FLUTTER% bytes
echo   📈 المساحة النهائية: %FINAL% bytes
echo.

echo 🎉 تم تحرير مساحة هائلة!
echo.

echo 🛡️  المشاريع المحمية (سليمة):
echo   ✅ legal2025 - محمي ونظيف
echo   ✅ اذكاري - محمي ونظيف
echo.

echo 💡 خطوات ما بعد التنظيف:
echo ========================
echo   1. ⚠️  إعادة تشغيل الكمبيوتر فوراً (مهم جداً)
echo   2. تشغيل: chkdsk C: /f /r
echo   3. تشغيل: sfc /scannow  
echo   4. إلغاء تثبيت البرامج غير المستخدمة
echo   5. نقل الملفات الكبيرة لقرص آخر
echo   6. تحديث Windows
echo.

echo 🚀 اختبار مشروع legal2025:
echo ========================
echo   cd D:\20223\2025\legl92025
echo   flutter clean
echo   flutter pub get
echo   flutter run -d chrome
echo.

echo 🎯 يجب أن تكون المساحة الآن كافية لتشغيل Firebase!
echo.

echo ⚠️  إعادة التشغيل مطلوبة لتطبيق جميع التغييرات
set /p restart="هل تريد إعادة تشغيل الكمبيوتر الآن؟ (Y/N): "
if /i "%restart%"=="Y" shutdown /r /t 10 /c "إعادة تشغيل بعد التنظيف الشامل"

:end
echo.
echo 📋 تقارير التنظيف محفوظة في المجلد الحالي
echo 🎉 التنظيف مكتمل!
echo.
pause
