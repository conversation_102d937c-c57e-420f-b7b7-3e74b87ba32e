import 'dart:math';
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server/gmail.dart';

/// خدمة بريد إلكتروني تعمل بشكل فعلي
class WorkingEmailService {
  // إعدادات Gmail للاختبار - حساب حقيقي
  static const String _senderEmail = '<EMAIL>';
  static const String _senderPassword = 'abcd efgh ijkl mnop';
  static const String _senderName = 'Legal 2025 App';

  /// إرسال كود التحقق عبر البريد الإلكتروني
  static Future<bool> sendVerificationCode(String email, String code) async {
    try {
      // إعداد خادم Gmail
      final smtpServer = gmail(_senderEmail, _senderPassword);

      // إنشاء الرسالة
      final message =
          Message()
            ..from = Address(_senderEmail, _senderName)
            ..recipients.add(email)
            ..subject = 'كود التحقق - تطبيق Legal 2025'
            ..html = _buildEmailTemplate(code);

      // إرسال البريد الإلكتروني
      final sendReport = await send(message, smtpServer);

      print('✅ تم إرسال البريد الإلكتروني بنجاح إلى: $email');
      print('📧 معرف الرسالة: ${sendReport.toString()}');

      return true;
    } catch (e) {
      print('❌ فشل في إرسال البريد الإلكتروني: $e');

      // عرض الكود في الكونسول كبديل
      _showCodeInConsole(email, code);

      return false;
    }
  }

  /// عرض الكود في الكونسول كبديل
  static void _showCodeInConsole(String email, String code) {
    print('');
    print('📧 ═══════════════════════════════════════');
    print('📧 فشل إرسال البريد الإلكتروني');
    print('📧 ═══════════════════════════════════════');
    print('📧 البريد المُرسل إليه: $email');
    print('📧 كود التحقق: $code');
    print('📧 ═══════════════════════════════════════');
    print('📧 يرجى استخدام الكود أعلاه للتحقق');
    print('📧 ═══════════════════════════════════════');
    print('');
  }

  /// إنشاء قالب البريد الإلكتروني
  static String _buildEmailTemplate(String code) {
    return '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>كود التحقق</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 20px;
                direction: rtl;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                background: white;
                border-radius: 20px;
                overflow: hidden;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 40px 20px;
                text-align: center;
                color: white;
            }
            .header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: bold;
            }
            .content {
                padding: 40px 30px;
                text-align: center;
            }
            .code-container {
                background: #f8f9ff;
                border: 3px dashed #667eea;
                border-radius: 15px;
                padding: 30px;
                margin: 30px 0;
            }
            .code {
                font-size: 36px;
                font-weight: bold;
                color: #667eea;
                letter-spacing: 8px;
                margin: 10px 0;
                font-family: 'Courier New', monospace;
            }
            .message {
                font-size: 16px;
                line-height: 1.6;
                color: #333;
                margin: 20px 0;
            }
            .warning {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 10px;
                padding: 15px;
                margin: 20px 0;
                color: #856404;
                font-size: 14px;
            }
            .footer {
                background: #f8f9fa;
                padding: 20px;
                text-align: center;
                color: #6c757d;
                font-size: 12px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔐 كود التحقق</h1>
                <p>تطبيق Legal 2025</p>
            </div>

            <div class="content">
                <h2>مرحباً بك!</h2>
                <p class="message">
                    لإكمال عملية إنشاء حسابك في تطبيق Legal 2025، يرجى استخدام كود التحقق التالي:
                </p>

                <div class="code-container">
                    <div class="code">$code</div>
                    <p>أدخل هذا الكود في التطبيق</p>
                </div>

                <div class="warning">
                    ⚠️ هذا الكود صالح لمدة 10 دقائق فقط ولا يمكن استخدامه أكثر من مرة واحدة.
                </div>

                <p class="message">
                    إذا لم تطلب هذا الكود، يرجى تجاهل هذه الرسالة.
                </p>
            </div>

            <div class="footer">
                <p>© 2025 Legal 2025. جميع الحقوق محفوظة.</p>
                <p>هذه رسالة تلقائية، يرجى عدم الرد عليها.</p>
            </div>
        </div>
    </body>
    </html>
    ''';
  }

  /// إنشاء كود تحقق عشوائي
  static String generateVerificationCode() {
    final random = Random();
    String code = '';
    for (int i = 0; i < 6; i++) {
      code += random.nextInt(10).toString();
    }
    return code;
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}
