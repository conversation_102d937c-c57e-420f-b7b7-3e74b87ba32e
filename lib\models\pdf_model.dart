class PDFModel {
  final String id;
  final String name;
  final String url;
  final String category; // أسئلة، امتحانات، ملخصات، الكتاب الرسمي
  final String subjectId;
  final String subjectName; // اسم المادة للعرض
  final String yearId;
  final String semesterId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String uploadedBy; // email الأدمن
  final String uploaderName; // اسم الأدمن
  final int downloadCount;
  final double fileSize; // بالميجابايت
  final String fileName; // اسم الملف الأصلي
  final String fileExtension; // امتداد الملف
  final bool isFromUrl; // هل الملف من رابط خارجي أم مرفوع
  final String? originalUrl; // الرابط الأصلي إذا كان من رابط خارجي
  final bool isActive; // هل الملف نشط أم محذوف
  final Map<String, dynamic>? metadata; // بيانات إضافية

  PDFModel({
    required this.id,
    required this.name,
    required this.url,
    required this.category,
    required this.subjectId,
    required this.subjectName,
    required this.yearId,
    required this.semesterId,
    required this.createdAt,
    required this.updatedAt,
    required this.uploadedBy,
    required this.uploaderName,
    this.downloadCount = 0,
    this.fileSize = 0.0,
    required this.fileName,
    this.fileExtension = 'pdf',
    this.isFromUrl = false,
    this.originalUrl,
    this.isActive = true,
    this.metadata,
  });

  factory PDFModel.fromJson(Map<String, dynamic> json) {
    return PDFModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      url: json['url'] ?? '',
      category: json['category'] ?? '',
      subjectId: json['subjectId'] ?? '',
      subjectName: json['subjectName'] ?? '',
      yearId: json['yearId'] ?? '',
      semesterId: json['semesterId'] ?? '',
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
      uploadedBy: json['uploadedBy'] ?? '',
      uploaderName: json['uploaderName'] ?? '',
      downloadCount: json['downloadCount'] ?? 0,
      fileSize: (json['fileSize'] ?? 0.0).toDouble(),
      fileName: json['fileName'] ?? '',
      fileExtension: json['fileExtension'] ?? 'pdf',
      isFromUrl: json['isFromUrl'] ?? false,
      originalUrl: json['originalUrl'],
      isActive: json['isActive'] ?? true,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'category': category,
      'subjectId': subjectId,
      'subjectName': subjectName,
      'yearId': yearId,
      'semesterId': semesterId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'uploadedBy': uploadedBy,
      'uploaderName': uploaderName,
      'downloadCount': downloadCount,
      'fileSize': fileSize,
      'fileName': fileName,
      'fileExtension': fileExtension,
      'isFromUrl': isFromUrl,
      'originalUrl': originalUrl,
      'isActive': isActive,
      'metadata': metadata ?? {},
    };
  }

  PDFModel copyWith({
    String? id,
    String? name,
    String? url,
    String? category,
    String? subjectId,
    String? subjectName,
    String? yearId,
    String? semesterId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? uploadedBy,
    String? uploaderName,
    int? downloadCount,
    double? fileSize,
    String? fileName,
    String? fileExtension,
    bool? isFromUrl,
    String? originalUrl,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return PDFModel(
      id: id ?? this.id,
      name: name ?? this.name,
      url: url ?? this.url,
      category: category ?? this.category,
      subjectId: subjectId ?? this.subjectId,
      subjectName: subjectName ?? this.subjectName,
      yearId: yearId ?? this.yearId,
      semesterId: semesterId ?? this.semesterId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      uploaderName: uploaderName ?? this.uploaderName,
      downloadCount: downloadCount ?? this.downloadCount,
      fileSize: fileSize ?? this.fileSize,
      fileName: fileName ?? this.fileName,
      fileExtension: fileExtension ?? this.fileExtension,
      isFromUrl: isFromUrl ?? this.isFromUrl,
      originalUrl: originalUrl ?? this.originalUrl,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  // دوال مساعدة
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '${fileSize.toStringAsFixed(0)} B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  String get displayName {
    return name.isNotEmpty ? name : fileName;
  }

  bool get isValidPDF {
    return url.isNotEmpty &&
        (url.toLowerCase().endsWith('.pdf') ||
            fileExtension.toLowerCase() == 'pdf');
  }
}

class AdminUser {
  final String email;
  final String name;
  final List<String> permissions;
  final DateTime createdAt;
  final bool isActive;

  AdminUser({
    required this.email,
    required this.name,
    required this.permissions,
    required this.createdAt,
    this.isActive = true,
  });

  factory AdminUser.fromJson(Map<String, dynamic> json) {
    return AdminUser(
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      permissions: List<String>.from(json['permissions'] ?? []),
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'name': name,
      'permissions': permissions,
      'createdAt': createdAt.toIso8601String(),
      'isActive': isActive,
    };
  }

  bool hasPermission(String permission) {
    return permissions.contains(permission) || permissions.contains('all');
  }
}

// صلاحيات الأدمن
class AdminPermissions {
  static const String addPDF = 'add_pdf';
  static const String editPDF = 'edit_pdf';
  static const String deletePDF = 'delete_pdf';
  static const String manageUsers = 'manage_users';
  static const String sendNotifications = 'send_notifications';
  static const String viewAnalytics = 'view_analytics';
  static const String all = 'all';

  static List<String> getAllPermissions() {
    return [
      addPDF,
      editPDF,
      deletePDF,
      manageUsers,
      sendNotifications,
      viewAnalytics,
    ];
  }
}
