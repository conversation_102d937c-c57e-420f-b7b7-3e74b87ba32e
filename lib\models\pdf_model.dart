class PDFModel {
  final String id;
  final String name;
  final String url;
  final String category; // أسئلة، امتحانات، ملخصات، الكتاب الرسمي
  final String subjectId;
  final String yearId;
  final String semesterId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String uploadedBy; // email الأدمن
  final int downloadCount;
  final double fileSize; // بالميجابايت

  PDFModel({
    required this.id,
    required this.name,
    required this.url,
    required this.category,
    required this.subjectId,
    required this.yearId,
    required this.semesterId,
    required this.createdAt,
    required this.updatedAt,
    required this.uploadedBy,
    this.downloadCount = 0,
    this.fileSize = 0.0,
  });

  factory PDFModel.fromJson(Map<String, dynamic> json) {
    return PDFModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      url: json['url'] ?? '',
      category: json['category'] ?? '',
      subjectId: json['subjectId'] ?? '',
      yearId: json['yearId'] ?? '',
      semesterId: json['semesterId'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      uploadedBy: json['uploadedBy'] ?? '',
      downloadCount: json['downloadCount'] ?? 0,
      fileSize: (json['fileSize'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'category': category,
      'subjectId': subjectId,
      'yearId': yearId,
      'semesterId': semesterId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'uploadedBy': uploadedBy,
      'downloadCount': downloadCount,
      'fileSize': fileSize,
    };
  }

  PDFModel copyWith({
    String? id,
    String? name,
    String? url,
    String? category,
    String? subjectId,
    String? yearId,
    String? semesterId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? uploadedBy,
    int? downloadCount,
    double? fileSize,
  }) {
    return PDFModel(
      id: id ?? this.id,
      name: name ?? this.name,
      url: url ?? this.url,
      category: category ?? this.category,
      subjectId: subjectId ?? this.subjectId,
      yearId: yearId ?? this.yearId,
      semesterId: semesterId ?? this.semesterId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      downloadCount: downloadCount ?? this.downloadCount,
      fileSize: fileSize ?? this.fileSize,
    );
  }
}

class AdminUser {
  final String email;
  final String name;
  final List<String> permissions;
  final DateTime createdAt;
  final bool isActive;

  AdminUser({
    required this.email,
    required this.name,
    required this.permissions,
    required this.createdAt,
    this.isActive = true,
  });

  factory AdminUser.fromJson(Map<String, dynamic> json) {
    return AdminUser(
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      permissions: List<String>.from(json['permissions'] ?? []),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'name': name,
      'permissions': permissions,
      'createdAt': createdAt.toIso8601String(),
      'isActive': isActive,
    };
  }

  bool hasPermission(String permission) {
    return permissions.contains(permission) || permissions.contains('all');
  }
}

// صلاحيات الأدمن
class AdminPermissions {
  static const String addPDF = 'add_pdf';
  static const String editPDF = 'edit_pdf';
  static const String deletePDF = 'delete_pdf';
  static const String manageUsers = 'manage_users';
  static const String sendNotifications = 'send_notifications';
  static const String viewAnalytics = 'view_analytics';
  static const String all = 'all';

  static List<String> getAllPermissions() {
    return [addPDF, editPDF, deletePDF, manageUsers, sendNotifications, viewAnalytics];
  }
}
