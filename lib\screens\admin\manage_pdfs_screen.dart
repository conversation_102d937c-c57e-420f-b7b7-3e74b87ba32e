import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../providers/admin_provider.dart';
import '../../providers/theme_provider.dart';
import '../../data/academic_data.dart';
import '../../models/subject.dart';
import '../../models/pdf_model.dart';
import '../../services/admin_service.dart';
import 'add_pdf_screen.dart';
import 'edit_pdf_screen.dart';

class ManagePDFsScreen extends StatefulWidget {
  final String? subjectId;
  final String? yearId;
  final String? semesterId;
  final String? subjectName;

  const ManagePDFsScreen({
    super.key,
    this.subjectId,
    this.yearId,
    this.semesterId,
    this.subjectName,
  });

  @override
  State<ManagePDFsScreen> createState() => _ManagePDFsScreenState();
}

class _ManagePDFsScreenState extends State<ManagePDFsScreen> {
  final List<AcademicYear> academicYears = AcademicData.getAcademicYears();
  final List<String> categories = [
    'أسئلة',
    'امتحانات',
    'ملخصات',
    'الكتاب الرسمي',
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer2<AdminProvider, ThemeProvider>(
      builder: (context, adminProvider, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          appBar: _buildAppBar(themeProvider),
          body:
              widget.subjectId != null
                  ? _buildSubjectPDFs(themeProvider, adminProvider)
                  : _buildFullStructure(themeProvider, adminProvider),
          floatingActionButton: _buildFAB(themeProvider),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeProvider themeProvider) {
    return AppBar(
      backgroundColor:
          themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
      elevation: 0,
      title: Text(
        widget.subjectName != null
            ? 'إدارة ملفات ${widget.subjectName}'
            : 'إدارة جميع ملفات PDF',
        style: GoogleFonts.cairo(
          fontWeight: FontWeight.w700,
          color:
              themeProvider.isDarkMode ? Colors.white : const Color(0xFF1F2937),
        ),
      ),
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color:
              themeProvider.isDarkMode ? Colors.white : const Color(0xFF1F2937),
        ),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildFullStructure(
    ThemeProvider themeProvider,
    AdminProvider adminProvider,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رسائل النجاح والخطأ
          if (adminProvider.error != null)
            _buildErrorMessage(
              adminProvider.error!,
              themeProvider,
              adminProvider,
            ),
          if (adminProvider.success != null)
            _buildSuccessMessage(
              adminProvider.success!,
              themeProvider,
              adminProvider,
            ),

          // عرض جميع الفرق
          ...academicYears.map((year) => _buildYearCard(year, themeProvider)),
        ],
      ),
    );
  }

  Widget _buildYearCard(AcademicYear year, ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6366F1).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.school,
                  color: Color(0xFF6366F1),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                year.arabicName,
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.w700,
                  fontSize: 18,
                  color:
                      themeProvider.isDarkMode
                          ? Colors.white
                          : const Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          children:
              year.semesters
                  .map(
                    (semester) =>
                        _buildSemesterCard(year, semester, themeProvider),
                  )
                  .toList(),
        ),
      ),
    );
  }

  Widget _buildSemesterCard(
    AcademicYear year,
    Semester semester,
    ThemeProvider themeProvider,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode
                ? const Color(0xFF334155)
                : const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Icon(
                  Icons.calendar_today,
                  color: Color(0xFF10B981),
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                semester.arabicName,
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color:
                      themeProvider.isDarkMode
                          ? Colors.white
                          : const Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          children:
              semester.subjects
                  .map(
                    (subject) => _buildSubjectCard(
                      year,
                      semester,
                      subject,
                      themeProvider,
                    ),
                  )
                  .toList(),
        ),
      ),
    );
  }

  Widget _buildSubjectCard(
    AcademicYear year,
    Semester semester,
    Subject subject,
    ThemeProvider themeProvider,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF475569) : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: const Color(0xFFF59E0B).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(
                  Icons.book,
                  color: Color(0xFFF59E0B),
                  size: 14,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  subject.arabicName,
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : const Color(0xFF1F2937),
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.add, size: 20),
                onPressed:
                    () => _navigateToAddPDF(
                      yearId: year.id,
                      semesterId: semester.id,
                      subjectId: subject.id,
                    ),
              ),
            ],
          ),
          children:
              categories
                  .map(
                    (category) => _buildCategorySection(
                      year,
                      semester,
                      subject,
                      category,
                      themeProvider,
                    ),
                  )
                  .toList(),
        ),
      ),
    );
  }

  Widget _buildCategorySection(
    AcademicYear year,
    Semester semester,
    Subject subject,
    String category,
    ThemeProvider themeProvider,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: _getCategoryColor(category).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                Icon(
                  _getCategoryIcon(category),
                  color: _getCategoryColor(category),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  category,
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                    color: _getCategoryColor(category),
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.add, size: 16),
                  onPressed:
                      () => _navigateToAddPDF(
                        yearId: year.id,
                        semesterId: semester.id,
                        subjectId: subject.id,
                        category: category,
                      ),
                ),
              ],
            ),
          ),

          // قائمة ملفات PDF
          StreamBuilder<QuerySnapshot>(
            stream: AdminService.getPDFsStream(
              subjectId: subject.id,
              category: category,
              yearId: year.id,
            ),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Padding(
                  padding: EdgeInsets.all(16),
                  child: Center(child: CircularProgressIndicator()),
                );
              }

              if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                return Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'لا توجد ملفات في هذا القسم',
                    style: GoogleFonts.cairo(color: Colors.grey, fontSize: 12),
                  ),
                );
              }

              final pdfs =
                  snapshot.data!.docs
                      .map(
                        (doc) => PDFModel.fromJson(
                          doc.data() as Map<String, dynamic>,
                        ),
                      )
                      .toList();

              return Column(
                children:
                    pdfs
                        .map((pdf) => _buildPDFItem(pdf, themeProvider))
                        .toList(),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPDFItem(PDFModel pdf, ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode
                ? const Color(0xFF64748B)
                : const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          const Icon(Icons.picture_as_pdf, color: Color(0xFFEF4444), size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  pdf.name,
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : const Color(0xFF1F2937),
                  ),
                ),
                Text(
                  'تم الرفع: ${_formatDate(pdf.createdAt)}',
                  style: GoogleFonts.cairo(fontSize: 10, color: Colors.grey),
                ),
              ],
            ),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, size: 16),
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _navigateToEditPDF(pdf);
                  break;
                case 'delete':
                  _showDeleteDialog(pdf);
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        const Icon(Icons.edit, size: 16),
                        const SizedBox(width: 8),
                        Text('تعديل', style: GoogleFonts.cairo(fontSize: 12)),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        const Icon(Icons.delete, size: 16, color: Colors.red),
                        const SizedBox(width: 8),
                        Text(
                          'حذف',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectPDFs(
    ThemeProvider themeProvider,
    AdminProvider adminProvider,
  ) {
    return StreamBuilder<QuerySnapshot>(
      stream: AdminService.getPDFsStream(
        subjectId: widget.subjectId,
        yearId: widget.yearId,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.folder_open, size: 64, color: Colors.grey),
                const SizedBox(height: 16),
                Text(
                  'لا توجد ملفات PDF',
                  style: GoogleFonts.cairo(fontSize: 18, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        final pdfs =
            snapshot.data!.docs
                .map(
                  (doc) =>
                      PDFModel.fromJson(doc.data() as Map<String, dynamic>),
                )
                .toList();

        // تجميع PDFs حسب الفئة
        final groupedPDFs = <String, List<PDFModel>>{};
        for (final pdf in pdfs) {
          groupedPDFs.putIfAbsent(pdf.category, () => []).add(pdf);
        }

        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // رسائل النجاح والخطأ
            if (adminProvider.error != null)
              _buildErrorMessage(
                adminProvider.error!,
                themeProvider,
                adminProvider,
              ),
            if (adminProvider.success != null)
              _buildSuccessMessage(
                adminProvider.success!,
                themeProvider,
                adminProvider,
              ),

            ...groupedPDFs.entries.map(
              (entry) =>
                  _buildCategoryGroup(entry.key, entry.value, themeProvider),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCategoryGroup(
    String category,
    List<PDFModel> pdfs,
    ThemeProvider themeProvider,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _getCategoryColor(category).withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getCategoryIcon(category),
                  color: _getCategoryColor(category),
                ),
                const SizedBox(width: 12),
                Text(
                  category,
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w700,
                    fontSize: 18,
                    color: _getCategoryColor(category),
                  ),
                ),
                const Spacer(),
                Text(
                  '${pdfs.length} ملف',
                  style: GoogleFonts.cairo(
                    color: _getCategoryColor(category),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          ...pdfs.map((pdf) => _buildPDFListItem(pdf, themeProvider)),
        ],
      ),
    );
  }

  Widget _buildPDFListItem(PDFModel pdf, ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.1)),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFEF4444).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.picture_as_pdf,
              color: Color(0xFFEF4444),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  pdf.name,
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : const Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'تم الرفع: ${_formatDate(pdf.createdAt)} • ${pdf.downloadCount} تحميل',
                  style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _navigateToEditPDF(pdf);
                  break;
                case 'delete':
                  _showDeleteDialog(pdf);
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        const Icon(Icons.edit),
                        const SizedBox(width: 8),
                        Text('تعديل', style: GoogleFonts.cairo()),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        const Icon(Icons.delete, color: Colors.red),
                        const SizedBox(width: 8),
                        Text(
                          'حذف',
                          style: GoogleFonts.cairo(color: Colors.red),
                        ),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
    );
  }

  Widget _buildFAB(ThemeProvider themeProvider) {
    return FloatingActionButton.extended(
      onPressed:
          () => _navigateToAddPDF(
            yearId: widget.yearId,
            semesterId: widget.semesterId,
            subjectId: widget.subjectId,
          ),
      backgroundColor: const Color(0xFF6366F1),
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: Text(
        'إضافة PDF',
        style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
      ),
    );
  }

  Widget _buildErrorMessage(
    String message,
    ThemeProvider themeProvider,
    AdminProvider adminProvider,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFEF4444).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFEF4444).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.error, color: Color(0xFFEF4444)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: GoogleFonts.cairo(color: const Color(0xFFEF4444)),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Color(0xFFEF4444)),
            onPressed: () => adminProvider.clearMessages(),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessMessage(
    String message,
    ThemeProvider themeProvider,
    AdminProvider adminProvider,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF10B981).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF10B981).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.check_circle, color: Color(0xFF10B981)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: GoogleFonts.cairo(color: const Color(0xFF10B981)),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Color(0xFF10B981)),
            onPressed: () => adminProvider.clearMessages(),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'أسئلة':
        return const Color(0xFF6366F1);
      case 'امتحانات':
        return const Color(0xFFEF4444);
      case 'ملخصات':
        return const Color(0xFF10B981);
      case 'الكتاب الرسمي':
        return const Color(0xFFF59E0B);
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'أسئلة':
        return Icons.quiz;
      case 'امتحانات':
        return Icons.assignment;
      case 'ملخصات':
        return Icons.summarize;
      case 'الكتاب الرسمي':
        return Icons.menu_book;
      default:
        return Icons.folder;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _navigateToAddPDF({
    String? yearId,
    String? semesterId,
    String? subjectId,
    String? category,
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => AddPDFScreen(
              preSelectedYearId: yearId,
              preSelectedSemesterId: semesterId,
              preSelectedSubjectId: subjectId,
              preSelectedCategory: category,
            ),
      ),
    );
  }

  void _navigateToEditPDF(PDFModel pdf) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => EditPDFScreen(pdf: pdf)),
    );
  }

  void _showDeleteDialog(PDFModel pdf) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('حذف الملف', style: GoogleFonts.cairo()),
            content: Text(
              'هل أنت متأكد من حذف "${pdf.name}"؟\nسيتم إرسال إشعار لجميع المستخدمين.',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  context.read<AdminProvider>().deletePDF(pdf.id);
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: Text(
                  'حذف',
                  style: GoogleFonts.cairo(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }
}
