@echo off
echo 🚀 تنظيف متقدم - البحث عن الملفات الكبيرة والمشاريع القديمة
echo ================================================================
echo.

echo ⚠️  تحذير: سيتم البحث عن وحذف:
echo   - مشاريع Flutter/Android القديمة (عدا legal2025 و اذكاري)
echo   - ملفات APK كبيرة
echo   - مجلدات node_modules
echo   - ملفات ISO/IMG كبيرة
echo   - مجلدات .git كبيرة
echo   - ملفات تطوير قديمة
echo.
pause

echo 📊 تحليل استخدام القرص...
echo.

REM إنشاء ملف تقرير
echo تقرير تنظيف القرص - %date% %time% > cleanup_report.txt
echo ================================================ >> cleanup_report.txt
echo. >> cleanup_report.txt

REM البحث عن مجلدات node_modules كبيرة
echo 1. البحث عن مجلدات node_modules...
for /d /r C:\ %%i in (node_modules) do (
    if exist "%%i" (
        echo وجد: %%i
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            echo   ❌ حذف node_modules: %%i
            echo حذف node_modules: %%i >> cleanup_report.txt
            rd /s /q "%%i" 2>nul
        ) else (
            echo   ✅ محمي: %%i
        )
    )
)

REM البحث عن مجلدات .dart_tool
echo 2. البحث عن مجلدات .dart_tool...
for /d /r C:\ %%i in (.dart_tool) do (
    if exist "%%i" (
        echo وجد: %%i
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            echo   ❌ حذف .dart_tool: %%i
            echo حذف .dart_tool: %%i >> cleanup_report.txt
            rd /s /q "%%i" 2>nul
        ) else (
            echo   ✅ محمي: %%i
        )
    )
)

REM البحث عن مجلدات build كبيرة
echo 3. البحث عن مجلدات build...
for /d /r C:\ %%i in (build) do (
    if exist "%%i" (
        echo وجد: %%i
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            echo   ❌ حذف build: %%i
            echo حذف build: %%i >> cleanup_report.txt
            rd /s /q "%%i" 2>nul
        ) else (
            echo   ✅ محمي: %%i
        )
    )
)

REM البحث عن ملفات APK كبيرة
echo 4. البحث عن ملفات APK...
for /r C:\ %%i in (*.apk) do (
    if exist "%%i" (
        echo وجد APK: %%i
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            echo   ❌ حذف APK: %%i
            echo حذف APK: %%i >> cleanup_report.txt
            del /q /f "%%i" 2>nul
        ) else (
            echo   ✅ محمي: %%i
        )
    )
)

REM البحث عن ملفات ISO/IMG كبيرة
echo 5. البحث عن ملفات ISO/IMG...
for /r C:\ %%i in (*.iso *.img) do (
    if exist "%%i" (
        echo وجد: %%i
        echo   ❌ حذف: %%i
        echo حذف ISO/IMG: %%i >> cleanup_report.txt
        del /q /f "%%i" 2>nul
    )
)

REM البحث عن مجلدات Android build
echo 6. البحث عن مجلدات Android build...
for /d /r C:\ %%i in (app) do (
    if exist "%%i\build" (
        echo وجد Android build: %%i\build
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            echo   ❌ حذف Android build: %%i\build
            echo حذف Android build: %%i\build >> cleanup_report.txt
            rd /s /q "%%i\build" 2>nul
        ) else (
            echo   ✅ محمي: %%i\build
        )
    )
)

REM البحث عن مجلدات .gradle
echo 7. البحث عن مجلدات .gradle...
for /d /r C:\ %%i in (.gradle) do (
    if exist "%%i" (
        echo وجد: %%i
        echo %%i | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            echo   ❌ حذف .gradle: %%i
            echo حذف .gradle: %%i >> cleanup_report.txt
            rd /s /q "%%i" 2>nul
        ) else (
            echo   ✅ محمي: %%i
        )
    )
)

REM البحث عن مشاريع Android Studio القديمة
echo 8. البحث عن مشاريع Android Studio...
for /d %%i in ("C:\Users\<USER>\AndroidStudioProjects\*") do (
    if exist "%%i" (
        echo وجد مشروع Android: %%~ni
        echo %%~ni | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            echo   ❌ حذف مشروع Android: %%i
            echo حذف مشروع Android: %%i >> cleanup_report.txt
            rd /s /q "%%i" 2>nul
        ) else (
            echo   ✅ محمي: %%i
        )
    )
)

REM تنظيف مجلد Downloads من الملفات الكبيرة
echo 9. تنظيف مجلد Downloads...
for %%i in ("C:\Users\<USER>\Downloads\*.zip" "C:\Users\<USER>\Downloads\*.rar" "C:\Users\<USER>\Downloads\*.7z" "C:\Users\<USER>\Downloads\*.exe") do (
    if exist "%%i" (
        echo وجد في Downloads: %%i
        echo   ❌ حذف: %%i
        echo حذف من Downloads: %%i >> cleanup_report.txt
        del /q /f "%%i" 2>nul
    )
)

REM تنظيف مجلد Desktop من المشاريع القديمة
echo 10. تنظيف Desktop من المشاريع...
for /d %%i in ("C:\Users\<USER>\Desktop\*") do (
    if exist "%%i\pubspec.yaml" (
        echo وجد مشروع Flutter في Desktop: %%~ni
        echo %%~ni | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            echo   ❌ حذف من Desktop: %%i
            echo حذف مشروع من Desktop: %%i >> cleanup_report.txt
            rd /s /q "%%i" 2>nul
        ) else (
            echo   ✅ محمي في Desktop: %%i
        )
    )
)

REM تنظيف Visual Studio Code cache
echo 11. تنظيف VS Code cache...
if exist "%APPDATA%\Code\User\workspaceStorage" (
    echo   تنظيف VS Code workspace storage...
    rd /s /q "%APPDATA%\Code\User\workspaceStorage" 2>nul
    echo حذف VS Code cache >> cleanup_report.txt
)

REM تنظيف Chrome cache إضافي
echo 12. تنظيف Chrome cache متقدم...
taskkill /f /im chrome.exe 2>nul
if exist "%LOCALAPPDATA%\Google\Chrome\User Data\Default" (
    rd /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache" 2>nul
    rd /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Code Cache" 2>nul
    rd /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\GPUCache" 2>nul
    echo حذف Chrome cache متقدم >> cleanup_report.txt
)

REM تنظيف Flutter global cache
echo 13. تنظيف Flutter global cache...
if exist "%USERPROFILE%\.pub-cache" (
    echo   تنظيف .pub-cache...
    rd /s /q "%USERPROFILE%\.pub-cache\hosted" 2>nul
    echo حذف Flutter pub cache >> cleanup_report.txt
)

echo.
echo ✅ تم الانتهاء من التنظيف المتقدم!
echo.

echo 📋 تقرير التنظيف محفوظ في: cleanup_report.txt
echo.

echo 📊 فحص مساحة القرص النهائي...
dir C:\ | find "bytes free"
echo.

echo 🎉 التنظيف مكتمل!
echo المشاريع المحمية:
echo   ✅ legal2025 - محمي بالكامل
echo   ✅ اذكاري - محمي بالكامل
echo.

echo 💡 خطوات إضافية موصى بها:
echo   1. إعادة تشغيل الكمبيوتر
echo   2. تشغيل Disk Defragmenter
echo   3. فحص الأخطاء: chkdsk C: /f
echo   4. تنظيف Registry بأداة CCleaner
echo.

pause
