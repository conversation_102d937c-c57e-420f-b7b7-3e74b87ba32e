@echo off
title البحث عن الملفات والمجلدات الكبيرة
color 0E

echo.
echo 🔍 البحث عن الملفات والمجلدات الكبيرة في قرص C
echo ===============================================
echo.

echo 📊 فحص مساحة القرص الحالية...
dir C:\ | find "bytes free"
echo.

echo 🔍 جاري البحث عن أكبر الملفات والمجلدات...
echo هذا قد يستغرق بضع دقائق...
echo.

REM إنشاء ملف تقرير
set REPORT=large_files_report_%date:~-4%%date:~3,2%%date:~0,2%.txt
echo تقرير الملفات والمجلدات الكبيرة - %date% %time% > %REPORT%
echo ================================================ >> %REPORT%
echo. >> %REPORT%

echo 📁 البحث عن أكبر المجلدات...
echo أكبر المجلدات: >> %REPORT%
echo ============== >> %REPORT%

REM البحث في المجلدات الرئيسية
for /d %%i in ("C:\Users\<USER>\*") do (
    if exist "%%i" (
        for /f "tokens=3" %%a in ('dir "%%i" /s /-c 2^>nul ^| find "File(s)"') do (
            if %%a GTR 1000000000 (
                echo %%i - %%a bytes
                echo %%i - %%a bytes >> %REPORT%
            )
        )
    )
)

echo.
echo 📄 البحث عن أكبر الملفات...
echo أكبر الملفات: >> %REPORT%
echo ============= >> %REPORT%

REM البحث عن ملفات أكبر من 100MB
echo البحث عن ملفات أكبر من 100MB...
forfiles /p C:\ /s /m *.* /c "cmd /c if @fsize GTR 104857600 echo @path - @fsize bytes" 2>nul >> %REPORT%

echo.
echo 🗂️ فحص مجلدات محددة...

REM فحص مجلد Windows
echo فحص مجلد Windows...
echo مجلد Windows: >> %REPORT%
for /f "tokens=3" %%a in ('dir "C:\Windows" /s /-c 2^>nul ^| find "File(s)"') do (
    echo C:\Windows - %%a bytes
    echo C:\Windows - %%a bytes >> %REPORT%
)

REM فحص مجلد Program Files
echo فحص مجلد Program Files...
echo مجلد Program Files: >> %REPORT%
for /f "tokens=3" %%a in ('dir "C:\Program Files" /s /-c 2^>nul ^| find "File(s)"') do (
    echo C:\Program Files - %%a bytes
    echo C:\Program Files - %%a bytes >> %REPORT%
)

REM فحص مجلد Program Files (x86)
if exist "C:\Program Files (x86)" (
    echo فحص مجلد Program Files (x86)...
    echo مجلد Program Files (x86): >> %REPORT%
    for /f "tokens=3" %%a in ('dir "C:\Program Files (x86)" /s /-c 2^>nul ^| find "File(s)"') do (
        echo C:\Program Files (x86) - %%a bytes
        echo C:\Program Files (x86) - %%a bytes >> %REPORT%
    )
)

REM فحص مجلد Users
echo فحص مجلد Users...
echo مجلد Users: >> %REPORT%
for /f "tokens=3" %%a in ('dir "C:\Users" /s /-c 2^>nul ^| find "File(s)"') do (
    echo C:\Users - %%a bytes
    echo C:\Users - %%a bytes >> %REPORT%
)

echo.
echo 🔍 البحث عن أنواع ملفات محددة...

echo ملفات محددة: >> %REPORT%
echo ============ >> %REPORT%

REM البحث عن ملفات ISO
echo البحث عن ملفات ISO...
for /r C:\ %%i in (*.iso) do (
    if exist "%%i" (
        for %%j in ("%%i") do (
            echo ISO: %%i - %%~zj bytes
            echo ISO: %%i - %%~zj bytes >> %REPORT%
        )
    )
)

REM البحث عن ملفات APK
echo البحث عن ملفات APK...
for /r C:\ %%i in (*.apk) do (
    if exist "%%i" (
        for %%j in ("%%i") do (
            echo APK: %%i - %%~zj bytes
            echo APK: %%i - %%~zj bytes >> %REPORT%
        )
    )
)

REM البحث عن ملفات ZIP كبيرة
echo البحث عن ملفات ZIP كبيرة...
for /r C:\ %%i in (*.zip *.rar *.7z) do (
    if exist "%%i" (
        for %%j in ("%%i") do (
            if %%~zj GTR 52428800 (
                echo Archive: %%i - %%~zj bytes
                echo Archive: %%i - %%~zj bytes >> %REPORT%
            )
        )
    )
)

REM البحث عن ملفات فيديو كبيرة
echo البحث عن ملفات فيديو كبيرة...
for /r C:\ %%i in (*.mp4 *.avi *.mkv *.mov *.wmv) do (
    if exist "%%i" (
        for %%j in ("%%i") do (
            if %%~zj GTR 104857600 (
                echo Video: %%i - %%~zj bytes
                echo Video: %%i - %%~zj bytes >> %REPORT%
            )
        )
    )
)

echo.
echo 🗂️ فحص مجلدات التطوير...

echo مجلدات التطوير: >> %REPORT%
echo =============== >> %REPORT%

REM البحث عن مجلدات node_modules
echo البحث عن مجلدات node_modules...
for /d /r C:\ %%i in (node_modules) do (
    if exist "%%i" (
        for /f "tokens=3" %%a in ('dir "%%i" /s /-c 2^>nul ^| find "File(s)"') do (
            echo node_modules: %%i - %%a bytes
            echo node_modules: %%i - %%a bytes >> %REPORT%
        )
    )
)

REM البحث عن مجلدات build
echo البحث عن مجلدات build...
for /d /r C:\ %%i in (build) do (
    if exist "%%i" (
        for /f "tokens=3" %%a in ('dir "%%i" /s /-c 2^>nul ^| find "File(s)"') do (
            if %%a GTR 10485760 (
                echo build: %%i - %%a bytes
                echo build: %%i - %%a bytes >> %REPORT%
            )
        )
    )
)

REM البحث عن مجلدات .git
echo البحث عن مجلدات .git...
for /d /r C:\ %%i in (.git) do (
    if exist "%%i" (
        for /f "tokens=3" %%a in ('dir "%%i" /s /-c 2^>nul ^| find "File(s)"') do (
            if %%a GTR 10485760 (
                echo .git: %%i - %%a bytes
                echo .git: %%i - %%a bytes >> %REPORT%
            )
        )
    )
)

echo.
echo ✅ تم الانتهاء من البحث!
echo.

echo 📋 التقرير الكامل محفوظ في: %REPORT%
echo.

echo 📊 ملخص النتائج:
echo ================
type %REPORT% | find "bytes" | sort /r

echo.
echo 💡 توصيات للتنظيف:
echo ==================
echo 1. احذف الملفات الكبيرة غير المستخدمة
echo 2. احذف مجلدات node_modules القديمة
echo 3. احذف ملفات ISO/APK غير المطلوبة
echo 4. احذف ملفات الفيديو الكبيرة
echo 5. نظف مجلدات build القديمة
echo 6. احذف مجلدات .git للمشاريع القديمة
echo.

echo 🚀 بعد مراجعة التقرير، استخدم EMERGENCY_CLEANUP.bat
echo.

pause
