# دليل حل مشاكل Firebase

## المشاكل الشائعة وحلولها

### 1. مشكلة "إنشاء الحسابات غير مفعل"

**الخطأ**: `operation-not-allowed`

**الحل**:
1. اذه<PERSON> إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروع `legal2025`
3. اذهب إلى **Authentication** > **Sign-in method**
4. فعّل **Email/Password** provider
5. تأكد من تفعيل **Enable** و **Email link (passwordless sign-in)**

### 2. مشكلة تسجيل الدخول المجهول

**الخطأ**: `anonymous sign-in is disabled`

**الحل**:
1. في Firebase Console > Authentication > Sign-in method
2. فعّل **Anonymous** provider
3. اضغط **Save**

### 3. مشكلة API Key غير صحيح

**الخطأ**: `API key not valid`

**الحل**:
1. تحقق من `lib/config/firebase_config.dart`
2. تأكد من أن API Keys صحيحة لكل منصة
3. قارن مع Firebase Console > Project Settings > General

### 4. مشكلة Auth Domain

**الخطأ**: `auth domain is not authorized`

**الحل**:
1. في Firebase Console > Authentication > Settings
2. اذهب إلى **Authorized domains**
3. أضف النطاقات المطلوبة:
   - `localhost`
   - `legal2025.firebaseapp.com`
   - أي نطاقات أخرى تستخدمها

### 5. مشكلة الشبكة

**الخطأ**: `network error` أو `timeout`

**الحل**:
1. تحقق من اتصال الإنترنت
2. تحقق من إعدادات Firewall
3. جرب VPN إذا كان هناك حجب

### 6. مشكلة Platform Configuration

**الخطأ**: `No Firebase App '[DEFAULT]' has been created`

**الحل**:
1. تأكد من استدعاء `FirebaseConfig.initialize()` في `main()`
2. تحقق من أن `firebase_config.dart` يحتوي على إعدادات صحيحة
3. تأكد من أن Package Name يطابق Firebase Console

## خطوات التشخيص

### 1. استخدم أداة التشخيص المدمجة
```dart
// في التطبيق، اذهب إلى صفحة تسجيل الدخول
// اضغط على "🩺 تشخيص Firebase"
```

### 2. فحص Firebase Console
1. **Project Overview**: تحقق من حالة المشروع
2. **Authentication**: تحقق من Providers المفعلة
3. **Usage**: تحقق من الحصص والاستخدام

### 3. فحص Network
```bash
# اختبار الاتصال
ping firebase.google.com
ping firebaseapp.com
```

### 4. فحص Logs
```bash
# في Flutter
flutter logs

# أو في Chrome DevTools
# افتح Developer Tools > Console
```

## إعدادات Firebase Console المطلوبة

### Authentication Providers
- ✅ **Email/Password**: مفعل
- ✅ **Anonymous**: مفعل
- ⚠️ **Google**: اختياري (يحتاج إعداد إضافي)
- ⚠️ **Facebook**: اختياري (يحتاج إعداد إضافي)

### Firestore Database
- ✅ **Database**: منشأ في وضع Test mode
- ✅ **Rules**: مفتوحة للقراءة والكتابة (مؤقتاً)

### Storage
- ✅ **Storage**: مفعل
- ✅ **Rules**: مفتوحة للقراءة والكتابة (مؤقتاً)

## كيفية إعادة تهيئة Firebase

### 1. إعادة تهيئة كاملة
```bash
# احذف التطبيق من Firebase Console
# أنشئ مشروع جديد
# حدّث firebase_config.dart
```

### 2. إعادة تهيئة الإعدادات فقط
```bash
# في Firebase Console
# اذهب إلى Project Settings
# أعد إنشاء Configuration files
```

## أكواد الأخطاء الشائعة

| كود الخطأ | المعنى | الحل |
|-----------|--------|------|
| `user-not-found` | المستخدم غير موجود | تحقق من البريد الإلكتروني |
| `wrong-password` | كلمة مرور خاطئة | تحقق من كلمة المرور |
| `email-already-in-use` | البريد مستخدم | استخدم بريد آخر أو سجل دخول |
| `weak-password` | كلمة مرور ضعيفة | استخدم كلمة مرور أقوى |
| `operation-not-allowed` | العملية غير مسموحة | فعّل Provider في Console |
| `network-request-failed` | خطأ شبكة | تحقق من الاتصال |

## الدعم والمساعدة

إذا استمرت المشاكل:
1. استخدم أداة التشخيص المدمجة
2. تحقق من Firebase Console
3. راجع Firebase Documentation
4. تحقق من Firebase Status Page

## ملاحظات مهمة

⚠️ **تحذير**: في الإنتاج، يجب:
- تحديث قواعد الأمان
- تقييد API Keys
- إعداد Monitoring
- تفعيل Security Rules
