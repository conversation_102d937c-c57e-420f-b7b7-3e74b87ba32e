# 🔥 حل مشكلة صلاحيات إضافة PDF

## 🚨 **المشكلة:**
التطبيق يظهر رسالة "ليس لديك صلاحيات إضافة PDF" عند محاولة إضافة ملف PDF جديد.

## ✅ **الحل السريع:**

### **الخطوة 1: تحديث قواعد Firestore**

1. **افتح Firebase Console:**
   - اذهب إلى: https://console.firebase.google.com
   - اختر مشروعك

2. **انتقل إلى Firestore Database:**
   - من القائمة الجانبية، اختر "Firestore Database"
   - اضغط على تبويب "Rules"

3. **استبدل القواعد الحالية بهذه القواعد:**

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // المستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // الأدمن - السماح للأدمن الرئيسي
    match /admins/{adminId} {
      allow read, write: if request.auth != null && 
        (request.auth.token.email == '<EMAIL>' || 
         resource.data.email == request.auth.token.email);
    }
    
    // ملفات PDF - الأدمن فقط يمكنه الكتابة
    match /pdfs/{pdfId} {
      allow read: if true;
      allow write: if request.auth != null && 
        request.auth.token.email == '<EMAIL>';
    }
    
    // الإشعارات - الأدمن فقط يمكنه الكتابة
    match /notifications/{notificationId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.token.email == '<EMAIL>';
    }
    
    // المنشورات
    match /posts/{postId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // التعليقات
    match /comments/{commentId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // الإعجابات
    match /likes/{likeId} {
      allow read, write: if request.auth != null;
    }
    
    // المشاركات
    match /shares/{shareId} {
      allow read, write: if request.auth != null;
    }
    
    // الاستطلاعات
    match /polls/{pollId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // أصوات الاستطلاعات
    match /poll_votes/{voteId} {
      allow read, write: if request.auth != null;
    }
    
    // المحادثات
    match /chats/{chatId} {
      allow read, write: if request.auth != null;
    }
    
    // رسائل المحادثة
    match /chat_messages/{messageId} {
      allow read, write: if request.auth != null;
    }
    
    // التسجيلات المؤقتة
    match /pending_registrations/{email} {
      allow read, write: if true;
    }
  }
}
```

4. **اضغط "Publish" لحفظ القواعد**

### **الخطوة 2: التحقق من تسجيل الدخول**

1. **تأكد من تسجيل الدخول بالبريد الصحيح:**
   - البريد: `<EMAIL>`
   - يجب أن يكون البريد محقق (verified)

2. **إذا لم يكن البريد مسجل:**
   - اذهب إلى Authentication في Firebase Console
   - أضف المستخدم يدوياً أو سجل حساب جديد

### **الخطوة 3: اختبار النظام**

1. **أعد تشغيل التطبيق**
2. **سجل دخول بحساب الأدمن**
3. **اذهب إلى لوحة الأدمن**
4. **جرب إضافة ملف PDF جديد**

## 🔧 **حل بديل للاختبار السريع:**

إذا كنت تريد اختبار النظام بسرعة، استخدم هذه القواعد المؤقتة:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

⚠️ **تحذير:** هذه القواعد تسمح لأي مستخدم مسجل بالقراءة والكتابة. استخدمها للاختبار فقط!

## 📊 **التحقق من نجاح الحل:**

بعد تطبيق القواعد، يجب أن ترى:

✅ **في التطبيق:**
- زر الأدمن يظهر في الشاشة الرئيسية
- إمكانية الوصول للوحة الأدمن
- إمكانية إضافة ملفات PDF بنجاح
- رسائل نجاح عند إضافة الملفات

✅ **في Firebase Console:**
- ملفات PDF تظهر في مجموعة `pdfs`
- إشعارات تظهر في مجموعة `notifications`

## 🆘 **إذا استمرت المشكلة:**

1. **تحقق من Console المتصفح:**
   - اضغط F12
   - ابحث عن رسائل خطأ Firebase

2. **تحقق من حالة Authentication:**
   - تأكد من تسجيل الدخول
   - تحقق من البريد الإلكتروني

3. **أعد تشغيل التطبيق:**
   - أغلق التطبيق تماماً
   - امسح cache المتصفح
   - أعد فتح التطبيق

4. **تحقق من الإعدادات:**
   - تأكد من أن المشروع الصحيح محدد
   - تحقق من ملف `firebase_options.dart`

## 📞 **للمساعدة الإضافية:**

إذا واجهت أي مشاكل:
1. تأكد من نسخ القواعد بالضبط
2. تحقق من عدم وجود أخطاء إملائية
3. تأكد من تفعيل جميع الخدمات في Firebase
4. جرب الحل البديل للاختبار السريع أولاً

---

**ملاحظة:** هذا الحل يركز على إصلاح مشكلة صلاحيات PDF فقط. باقي وظائف التطبيق ستعمل بشكل طبيعي.
