import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import '../models/pdf_model.dart';
import 'notification_service.dart';

/// خدمة إدارة ملفات PDF مع Firebase
class PDFService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  static const String pdfsCollection = 'pdfs';

  /// إضافة ملف PDF جديد
  static Future<bool> addPDF(PDFModel pdf) async {
    try {
      await _firestore.collection(pdfsCollection).doc(pdf.id).set(pdf.toJson());

      // إرسال إشعار للمستخدمين
      await NotificationService.sendNewPDFNotification(pdf);

      if (kDebugMode) {
        print('✅ تم إضافة PDF بنجاح: ${pdf.name}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة PDF: $e');
      }
      return false;
    }
  }

  /// رفع ملف PDF إلى Firebase Storage
  static Future<String?> uploadPDFFile(
    File file,
    String fileName, {
    String? customPath,
    Function(double)? onProgress,
  }) async {
    try {
      // إنشاء مسار منظم للملف
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = fileName.split('.').last.toLowerCase();
      final cleanFileName = fileName.replaceAll(RegExp(r'[^\w\s-.]'), '');
      final storagePath = customPath ?? 'pdfs/$timestamp-$cleanFileName';

      final ref = _storage.ref().child(storagePath);

      // إضافة metadata للملف
      final settableMetadata = SettableMetadata(
        contentType: 'application/pdf',
        customMetadata: {
          'originalName': fileName,
          'uploadedAt': DateTime.now().toIso8601String(),
          'fileExtension': fileExtension,
        },
      );

      // رفع الملف مع تتبع التقدم
      final uploadTask = ref.putFile(file, settableMetadata);

      // تتبع تقدم الرفع
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      if (kDebugMode) {
        print('✅ تم رفع الملف بنجاح:');
        print('   📁 المسار: $storagePath');
        print('   🔗 الرابط: $downloadUrl');
        print('   📊 الحجم: ${file.lengthSync()} bytes');
      }

      return downloadUrl;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في رفع الملف: $e');
      }
      return null;
    }
  }

  /// الحصول على ملفات PDF لمادة وقسم معين
  static Stream<List<PDFModel>> getPDFsStream({
    required String subjectId,
    required String category,
    String? yearId,
    String? semesterId,
  }) {
    Query query = _firestore
        .collection(pdfsCollection)
        .where('subjectId', isEqualTo: subjectId)
        .where('category', isEqualTo: category)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true);

    if (yearId != null) {
      query = query.where('yearId', isEqualTo: yearId);
    }

    if (semesterId != null) {
      query = query.where('semesterId', isEqualTo: semesterId);
    }

    return query.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return PDFModel.fromJson(data);
      }).toList();
    });
  }

  /// الحصول على جميع ملفات PDF
  static Stream<List<PDFModel>> getAllPDFsStream() {
    return _firestore
        .collection(pdfsCollection)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            final data = doc.data();
            return PDFModel.fromJson(data);
          }).toList();
        });
  }

  /// تحديث ملف PDF
  static Future<bool> updatePDF(
    String pdfId,
    Map<String, dynamic> updates,
  ) async {
    try {
      updates['updatedAt'] = DateTime.now().toIso8601String();

      await _firestore.collection(pdfsCollection).doc(pdfId).update(updates);

      if (kDebugMode) {
        print('✅ تم تحديث PDF بنجاح: $pdfId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث PDF: $e');
      }
      return false;
    }
  }

  /// حذف ملف PDF (حذف منطقي)
  static Future<bool> deletePDF(String pdfId) async {
    try {
      await _firestore.collection(pdfsCollection).doc(pdfId).update({
        'isActive': false,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('✅ تم حذف PDF بنجاح: $pdfId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف PDF: $e');
      }
      return false;
    }
  }

  /// حذف ملف PDF نهائياً (من قاعدة البيانات والتخزين)
  static Future<bool> deletePDFPermanently(
    String pdfId,
    String? storageUrl,
  ) async {
    try {
      // حذف من قاعدة البيانات
      await _firestore.collection(pdfsCollection).doc(pdfId).delete();

      // حذف من التخزين إذا كان موجود
      if (storageUrl != null && storageUrl.isNotEmpty) {
        try {
          final ref = _storage.refFromURL(storageUrl);
          await ref.delete();
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ تعذر حذف الملف من التخزين: $e');
          }
        }
      }

      if (kDebugMode) {
        print('✅ تم حذف PDF نهائياً: $pdfId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحذف النهائي للـ PDF: $e');
      }
      return false;
    }
  }

  /// زيادة عداد التحميل
  static Future<void> incrementDownloadCount(String pdfId) async {
    try {
      await _firestore.collection(pdfsCollection).doc(pdfId).update({
        'downloadCount': FieldValue.increment(1),
        'updatedAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث عداد التحميل: $e');
      }
    }
  }

  /// البحث في ملفات PDF
  static Stream<List<PDFModel>> searchPDFs(String searchTerm) {
    return _firestore
        .collection(pdfsCollection)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs
              .map((doc) {
                final data = doc.data();
                return PDFModel.fromJson(data);
              })
              .where(
                (pdf) =>
                    pdf.name.toLowerCase().contains(searchTerm.toLowerCase()) ||
                    pdf.subjectName.toLowerCase().contains(
                      searchTerm.toLowerCase(),
                    ) ||
                    pdf.category.toLowerCase().contains(
                      searchTerm.toLowerCase(),
                    ),
              )
              .toList();
        });
  }

  /// التحقق من صحة ملف PDF
  static bool isValidPDFFile(File file) {
    try {
      // التحقق من امتداد الملف
      final fileName = file.path.split('/').last.toLowerCase();
      if (!fileName.endsWith('.pdf')) {
        return false;
      }

      // التحقق من حجم الملف (أقل من 50 ميجا)
      final fileSizeInMB = file.lengthSync() / (1024 * 1024);
      if (fileSizeInMB > 50) {
        if (kDebugMode) {
          print('❌ حجم الملف كبير جداً: ${fileSizeInMB.toStringAsFixed(1)} MB');
        }
        return false;
      }

      // التحقق من أن الملف ليس فارغ
      if (file.lengthSync() == 0) {
        if (kDebugMode) {
          print('❌ الملف فارغ');
        }
        return false;
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التحقق من الملف: $e');
      }
      return false;
    }
  }

  /// الحصول على إحصائيات ملفات PDF
  static Future<Map<String, int>> getPDFStats() async {
    try {
      final snapshot =
          await _firestore
              .collection(pdfsCollection)
              .where('isActive', isEqualTo: true)
              .get();

      int totalPDFs = snapshot.docs.length;
      int totalDownloads = 0;
      Map<String, int> categoryCounts = {};

      for (var doc in snapshot.docs) {
        final data = doc.data();
        totalDownloads += (data['downloadCount'] ?? 0) as int;

        final category = data['category'] ?? 'غير محدد';
        categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
      }

      return {
        'totalPDFs': totalPDFs,
        'totalDownloads': totalDownloads,
        ...categoryCounts,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على إحصائيات PDF: $e');
      }
      return {};
    }
  }
}
