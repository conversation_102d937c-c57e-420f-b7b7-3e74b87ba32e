# 🚨 حل عاجل لمشكلة صلاحيات PDF

## المشكلة:
التطبيق يقول "ليس لديك صلاحيات إضافة PDF"

## الحل السريع (5 دقائق):

### 1. افتح Firebase Console
- اذهب إلى: https://console.firebase.google.com
- اختر مشروعك

### 2. انتقل إلى Firestore Database
- من القائمة الجانبية: "Firestore Database"
- اضغط تبويب "Rules"

### 3. استبدل القواعد بهذا الكود:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 4. اضغط "Publish"

### 5. أعد تشغيل التطبيق

---

## ✅ النتيجة المتوقعة:
- ستتمكن من إضافة ملفات PDF
- ستظهر رسالة "تم إضافة الملف بنجاح"
- الملفات ستظهر في Firebase Console

---

## ⚠️ ملاحظة مهمة:
هذه قواعد مؤقتة للاختبار. بعد التأكد من عمل النظام، يمكن تطبيق قواعد أكثر أماناً.

---

## 🔧 إذا استمرت المشكلة:
1. تأكد من تسجيل الدخول بالبريد: <EMAIL>
2. تحقق من تفعيل Authentication في Firebase
3. امسح cache المتصفح وأعد تشغيل التطبيق
