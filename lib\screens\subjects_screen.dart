import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../models/subject.dart';
import '../theme/app_theme.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';
import 'pdf_list_screen.dart';
import 'admin/add_pdf_screen.dart';
import 'admin/manage_pdfs_screen.dart';

class SubjectsScreen extends StatefulWidget {
  final AcademicYear year;

  const SubjectsScreen({super.key, required this.year});

  @override
  State<SubjectsScreen> createState() => _SubjectsScreenState();
}

class _SubjectsScreenState extends State<SubjectsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // حالة القوائم المنسدلة لكل مادة
  final Map<String, bool> _expandedSubjects = {};
  final Map<String, String> _selectedCategories = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.year.semesters.length,
      vsync: this,
    );

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// التحقق من صلاحيات الأدمن
  bool _isAdmin() {
    final authProvider = context.read<AuthProvider>();
    return authProvider.firebaseUser?.email == '<EMAIL>';
  }

  /// عرض خيارات الأدمن للمادة
  void _showAdminOptions(Subject subject) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return Container(
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                ),
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // مؤشر السحب
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // عنوان
                    Text(
                      'إدارة ${subject.arabicName}',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // خيارات الأدمن
                    _buildAdminOption(
                      icon: Icons.add_circle_outline,
                      title: 'إضافة ملف PDF',
                      subtitle: 'إضافة ملف جديد لهذه المادة',
                      color: const Color(0xFF10B981),
                      onTap: () {
                        Navigator.pop(context);
                        _navigateToAddPDF(subject);
                      },
                      themeProvider: themeProvider,
                    ),
                    const SizedBox(height: 12),
                    _buildAdminOption(
                      icon: Icons.edit_outlined,
                      title: 'إدارة الملفات',
                      subtitle: 'تعديل وحذف الملفات الموجودة',
                      color: const Color(0xFF3B82F6),
                      onTap: () {
                        Navigator.pop(context);
                        _navigateToManagePDFs(subject);
                      },
                      themeProvider: themeProvider,
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              );
            },
          ),
    );
  }

  /// بناء خيار أدمن
  Widget _buildAdminOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    required ThemeProvider themeProvider,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF334155)
                      : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            textDirection: TextDirection.rtl,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                    ),
                    Text(
                      subtitle,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF64748B)
                        : const Color(0xFF9CA3AF),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// التنقل إلى صفحة إضافة PDF
  void _navigateToAddPDF(Subject subject) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => AddPDFScreen(
              preSelectedYearId: widget.year.id,
              preSelectedSemesterId: widget.year.semesters.first.id,
              preSelectedSubjectId: subject.id,
            ),
      ),
    );
  }

  /// التنقل إلى صفحة إدارة PDFs
  void _navigateToManagePDFs(Subject subject) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ManagePDFsScreen(
              yearId: widget.year.id,
              semesterId: widget.year.semesters.first.id,
              subjectId: subject.id,
              subjectName: subject.arabicName,
            ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // الهيدر مع العنوان والتبويبات
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.primaryColor,
                    AppTheme.primaryColor.withValues(alpha: 0.8),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // شريط العنوان
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.white,
                            ),
                            style: IconButton.styleFrom(
                              padding: const EdgeInsets.all(8),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                widget.year.arabicName,
                                style: Theme.of(
                                  context,
                                ).textTheme.headlineMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                                textAlign: TextAlign.right,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // التبويبات
                  Consumer<ThemeProvider>(
                    builder: (context, themeProvider, child) {
                      return Container(
                        margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: TabBar(
                          controller: _tabController,
                          indicator: BoxDecoration(
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF1E293B)
                                    : Colors.white,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          indicatorSize: TabBarIndicatorSize.tab,
                          dividerColor: Colors.transparent,
                          labelColor:
                              themeProvider.isDarkMode
                                  ? const Color(0xFFF1F5F9)
                                  : AppTheme.primaryColor,
                          unselectedLabelColor: Colors.white.withValues(
                            alpha: 0.8,
                          ),
                          labelStyle: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.w600),
                          unselectedLabelStyle:
                              Theme.of(context).textTheme.titleMedium,
                          tabs:
                              widget.year.semesters.map((semester) {
                                return Tab(text: semester.arabicName);
                              }).toList(),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            // محتوى التبويبات
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: TabBarView(
                  controller: _tabController,
                  children:
                      widget.year.semesters.map((semester) {
                        return _buildSemesterContent(semester);
                      }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSemesterContent(Semester semester) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          color:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            physics: const BouncingScrollPhysics(),
            itemCount: semester.subjects.length,
            itemBuilder: (context, index) {
              final subject = semester.subjects[index];
              return _buildExpandableSubjectCard(subject);
            },
          ),
        );
      },
    );
  }

  Widget _buildExpandableSubjectCard(Subject subject) {
    final isExpanded = _expandedSubjects[subject.id] ?? false;

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(16),
            border:
                themeProvider.isDarkMode
                    ? Border.all(color: const Color(0xFF334155), width: 1)
                    : null,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: themeProvider.isDarkMode ? 0.3 : 0.08,
                ),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              // رأس المادة
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _expandedSubjects[subject.id] = !isExpanded;
                    });
                  },
                  borderRadius: BorderRadius.circular(16),
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      textDirection: TextDirection.rtl,
                      children: [
                        // أيقونة المادة
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppTheme.primaryColor,
                                AppTheme.primaryColor.withValues(alpha: 0.7),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.primaryColor.withValues(
                                  alpha: 0.3,
                                ),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.menu_book_rounded,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),

                        // معلومات المادة
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                subject.arabicName,
                                style: GoogleFonts.cairo(
                                  fontWeight: FontWeight.w700,
                                  color:
                                      themeProvider.isDarkMode
                                          ? const Color(0xFFF1F5F9)
                                          : const Color(0xFF1F2937),
                                  fontSize: 16,
                                ),
                                textAlign: TextAlign.start,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 8),
                              Row(
                                textDirection: TextDirection.rtl,
                                children: [
                                  _buildInfoChip(
                                    icon: Icons.play_circle_outline,
                                    count: subject.lecturesCount,
                                    label: 'محاضرات',
                                    color: const Color(0xFF10B981),
                                  ),
                                  const SizedBox(width: 8),
                                  _buildInfoChip(
                                    icon: Icons.assignment_outlined,
                                    count: subject.examsCount,
                                    label: 'امتحانات',
                                    color: const Color(0xFF3B82F6),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        // زر الأدمن (إذا كان المستخدم أدمن)
                        if (_isAdmin())
                          Padding(
                            padding: const EdgeInsets.only(left: 8),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: const Color(0xFFEF4444),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: InkWell(
                                onTap: () => _showAdminOptions(subject),
                                child: Icon(
                                  Icons.admin_panel_settings,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ),

                        // سهم التوسيع
                        AnimatedRotation(
                          turns: isExpanded ? 0.5 : 0,
                          duration: const Duration(milliseconds: 300),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF3F4F6),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.keyboard_arrow_down_rounded,
                              color: const Color(0xFF6B7280),
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // المحتوى المنسدل
              AnimatedSize(
                duration: const Duration(milliseconds: 400),
                curve: Curves.easeInOutCubic,
                child:
                    isExpanded
                        ? _buildSubjectOptions(subject)
                        : const SizedBox.shrink(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        textDirection: TextDirection.rtl,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 14),
          const SizedBox(width: 4),
          Text(
            '$count',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.w600,
              color: color,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectOptions(Subject subject) {
    // تحديد ما إذا كانت المادة هي القرآن الكريم
    final isQuranSubject =
        subject.arabicName.contains('القرآن') ||
        subject.arabicName.contains('قرآن') ||
        subject.arabicName.toLowerCase().contains('quran') ||
        subject.arabicName.toLowerCase().contains('quraan');

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
      child: Column(
        children: [
          // فاصل مع تأثير متدرج
          Container(
            height: 1,
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  AppTheme.primaryColor.withValues(alpha: 0.3),
                  Colors.transparent,
                ],
              ),
            ),
          ),

          // الخيارات حسب نوع المادة
          if (isQuranSubject)
            _buildQuranOptions(subject)
          else
            _buildRegularSubjectOptions(subject),
        ],
      ),
    );
  }

  Widget _buildQuranOptions(Subject subject) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 2.2,
      children: [
        _buildOptionCard(
          title: 'أشهر المواضع',
          icon: Icons.star_rounded,
          color: const Color(0xFFFFB800),
          onTap: () => _openPDFCategory(subject, 'أشهر المواضع'),
        ),
        _buildOptionCard(
          title: 'امتحانات',
          icon: Icons.assignment_rounded,
          color: const Color(0xFFEF4444),
          onTap: () => _openPDFCategory(subject, 'امتحانات'),
        ),
      ],
    );
  }

  Widget _buildRegularSubjectOptions(Subject subject) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 2.2,
      children: [
        _buildOptionCard(
          title: 'أسئلة',
          icon: Icons.quiz_rounded,
          color: const Color(0xFF3B82F6),
          onTap: () => _openPDFCategory(subject, 'أسئلة'),
        ),
        _buildOptionCard(
          title: 'امتحانات',
          icon: Icons.assignment_rounded,
          color: const Color(0xFFEF4444),
          onTap: () => _openPDFCategory(subject, 'امتحانات'),
        ),
        _buildOptionCard(
          title: 'ملخصات',
          icon: Icons.summarize_rounded,
          color: const Color(0xFF10B981),
          onTap: () => _openPDFCategory(subject, 'ملخصات'),
        ),
        _buildOptionCard(
          title: 'الكتاب الرسمي',
          icon: Icons.menu_book_rounded,
          color: const Color(0xFF8B5CF6),
          onTap: () => _openPDFCategory(subject, 'الكتاب الرسمي'),
        ),
      ],
    );
  }

  Widget _buildOptionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(16),
            splashColor: color.withValues(alpha: 0.2),
            highlightColor: color.withValues(alpha: 0.1),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors:
                      themeProvider.isDarkMode
                          ? [
                            color.withValues(alpha: 0.2),
                            color.withValues(alpha: 0.1),
                          ]
                          : [
                            color.withValues(alpha: 0.12),
                            color.withValues(alpha: 0.06),
                          ],
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color:
                      themeProvider.isDarkMode
                          ? color.withValues(alpha: 0.5)
                          : color.withValues(alpha: 0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(
                      alpha: themeProvider.isDarkMode ? 0.2 : 0.1,
                    ),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                textDirection: TextDirection.rtl,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [color, color.withValues(alpha: 0.8)],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: color.withValues(alpha: 0.3),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(icon, color: Colors.white, size: 20),
                  ),
                  const SizedBox(width: 10),
                  Flexible(
                    child: Text(
                      title,
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.w700,
                        color: color,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _openPDFCategory(Subject subject, String category) {
    _selectedCategories[subject.id] = category;

    // إنشاء قائمة PDFs حسب الفئة
    List<String> pdfs = _getPDFsForCategory(subject, category);

    if (pdfs.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'لا توجد ملفات متاحة في قسم $category حالياً',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: const Color(0xFFEF4444),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
      return;
    }

    // فتح شاشة عرض PDFs
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                PDFListScreen(subject: subject, category: category, pdfs: pdfs),
      ),
    );
  }

  List<String> _getPDFsForCategory(Subject subject, String category) {
    // محاكاة ملفات PDF حسب الفئة
    switch (category) {
      case 'أسئلة':
        return [
          'أسئلة_منتصف_الترم.pdf',
          'أسئلة_نهاية_الترم.pdf',
          'أسئلة_سابقة.pdf',
          'أسئلة_تدريبية.pdf',
        ];
      case 'امتحانات':
        return [
          'امتحان_2023.pdf',
          'امتحان_2022.pdf',
          'امتحان_2021.pdf',
          'امتحان_2020.pdf',
        ];
      case 'ملخصات':
        return [
          'ملخص_الفصل_الأول.pdf',
          'ملخص_الفصل_الثاني.pdf',
          'ملخص_شامل.pdf',
          'ملخص_مراجعة_نهائية.pdf',
        ];
      case 'الكتاب الرسمي':
        return ['${subject.arabicName}_الكتاب_الرسمي.pdf'];
      case 'أشهر المواضع':
        return [
          'أشهر_المواضع_في_القرآن.pdf',
          'المواضع_المهمة_للامتحان.pdf',
          'مواضع_مختارة.pdf',
        ];
      default:
        return [];
    }
  }
}
