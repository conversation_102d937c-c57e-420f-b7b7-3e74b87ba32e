import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../providers/admin_provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../data/academic_data.dart';
import '../../models/subject.dart';
import 'add_pdf_screen.dart';
import 'manage_pdfs_screen.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  final List<AcademicYear> academicYears = AcademicData.getAcademicYears();

  @override
  void initState() {
    super.initState();
    _checkAdminStatus();
  }

  Future<void> _checkAdminStatus() async {
    final authProvider = context.read<AuthProvider>();
    final adminProvider = context.read<AdminProvider>();

    if (authProvider.firebaseUser?.email != null) {
      await adminProvider.checkAdminStatus(authProvider.firebaseUser!.email!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<AdminProvider, ThemeProvider, AuthProvider>(
      builder: (context, adminProvider, themeProvider, authProvider, child) {
        // التحقق المباشر من الأدمن
        final userEmail = authProvider.firebaseUser?.email;
        final isAdmin =
            userEmail == '<EMAIL>' || adminProvider.isAdmin;

        // رسالة تشخيصية (للتطوير فقط)
        if (kDebugMode) {
          print('🔍 تشخيص الأدمن:');
          print('   البريد الإلكتروني: $userEmail');
          print('   هل هو أدمن؟ $isAdmin');
          print('   AdminProvider.isAdmin: ${adminProvider.isAdmin}');
        }

        if (!isAdmin) {
          return _buildAccessDenied(themeProvider);
        }

        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          appBar: _buildAppBar(themeProvider, adminProvider),
          body: _buildDashboard(themeProvider, adminProvider),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(
    ThemeProvider themeProvider,
    AdminProvider adminProvider,
  ) {
    return AppBar(
      backgroundColor:
          themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
      elevation: 0,
      title: Text(
        'لوحة تحكم الأدمن',
        style: GoogleFonts.cairo(
          fontWeight: FontWeight.w700,
          color:
              themeProvider.isDarkMode ? Colors.white : const Color(0xFF1F2937),
        ),
      ),
      actions: [
        // إشعار الحالة
        Container(
          margin: const EdgeInsets.only(left: 16),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: const Color(0xFF10B981),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.admin_panel_settings,
                color: Colors.white,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                'أدمن نشط',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDashboard(
    ThemeProvider themeProvider,
    AdminProvider adminProvider,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رسائل النجاح والخطأ
          if (adminProvider.error != null)
            _buildErrorMessage(adminProvider.error!, themeProvider),
          if (adminProvider.success != null)
            _buildSuccessMessage(adminProvider.success!, themeProvider),

          // معلومات الأدمن
          _buildAdminInfo(themeProvider, adminProvider),

          const SizedBox(height: 24),

          // الإجراءات السريعة
          _buildQuickActions(themeProvider),

          const SizedBox(height: 24),

          // إدارة المواد
          _buildSubjectsManagement(themeProvider),
        ],
      ),
    );
  }

  Widget _buildAdminInfo(
    ThemeProvider themeProvider,
    AdminProvider adminProvider,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.admin_panel_settings,
              color: Color(0xFF6366F1),
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  adminProvider.currentAdmin?.name ?? 'أمير الشريف',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w700,
                    fontSize: 18,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : const Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  adminProvider.currentAdmin?.email ?? '<EMAIL>',
                  style: GoogleFonts.cairo(
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF94A3B8)
                            : const Color(0xFF6B7280),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF10B981).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    'صلاحيات كاملة',
                    style: GoogleFonts.cairo(
                      color: const Color(0xFF10B981),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(ThemeProvider themeProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.w700,
            fontSize: 20,
            color:
                themeProvider.isDarkMode
                    ? Colors.white
                    : const Color(0xFF1F2937),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                title: 'إضافة PDF',
                subtitle: 'رفع ملف جديد',
                icon: Icons.add_circle,
                color: const Color(0xFF10B981),
                onTap: () => _navigateToAddPDF(),
                themeProvider: themeProvider,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionCard(
                title: 'إدارة الملفات',
                subtitle: 'تعديل وحذف',
                icon: Icons.folder_open,
                color: const Color(0xFF6366F1),
                onTap: () => _navigateToManagePDFs(),
                themeProvider: themeProvider,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required ThemeProvider themeProvider,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color:
              themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.w700,
                fontSize: 16,
                color:
                    themeProvider.isDarkMode
                        ? Colors.white
                        : const Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: GoogleFonts.cairo(
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF94A3B8)
                        : const Color(0xFF6B7280),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectsManagement(ThemeProvider themeProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إدارة المواد',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.w700,
            fontSize: 20,
            color:
                themeProvider.isDarkMode
                    ? Colors.white
                    : const Color(0xFF1F2937),
          ),
        ),
        const SizedBox(height: 16),
        ...academicYears.map((year) => _buildYearCard(year, themeProvider)),
      ],
    );
  }

  Widget _buildYearCard(AcademicYear year, ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            year.arabicName,
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color:
                  themeProvider.isDarkMode
                      ? Colors.white
                      : const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 12),
          ...year.semesters.map(
            (semester) => ExpansionTile(
              title: Text(
                semester.arabicName,
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
              children:
                  semester.subjects
                      .map(
                        (subject) => ListTile(
                          title: Text(
                            subject.arabicName,
                            style: GoogleFonts.cairo(),
                          ),
                          trailing: IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed:
                                () => _navigateToSubjectPDFs(
                                  subject,
                                  year,
                                  semester,
                                ),
                          ),
                        ),
                      )
                      .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorMessage(String message, ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFEF4444).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFEF4444).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.error, color: Color(0xFFEF4444)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: GoogleFonts.cairo(color: const Color(0xFFEF4444)),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Color(0xFFEF4444)),
            onPressed: () => context.read<AdminProvider>().clearMessages(),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessMessage(String message, ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF10B981).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF10B981).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.check_circle, color: Color(0xFF10B981)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: GoogleFonts.cairo(color: const Color(0xFF10B981)),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Color(0xFF10B981)),
            onPressed: () => context.read<AdminProvider>().clearMessages(),
          ),
        ],
      ),
    );
  }

  Widget _buildAccessDenied(ThemeProvider themeProvider) {
    return Scaffold(
      backgroundColor:
          themeProvider.isDarkMode
              ? const Color(0xFF0F172A)
              : const Color(0xFFF8FAFC),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.block, size: 64, color: Color(0xFFEF4444)),
            const SizedBox(height: 16),
            Text(
              'ليس لديك صلاحية للوصول',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color:
                    themeProvider.isDarkMode
                        ? Colors.white
                        : const Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'هذه الصفحة مخصصة للأدمن فقط',
              style: GoogleFonts.cairo(
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF94A3B8)
                        : const Color(0xFF6B7280),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.symmetric(horizontal: 32),
              decoration: BoxDecoration(
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF1E293B)
                        : Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFFEF4444).withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                children: [
                  Text(
                    'معلومات التشخيص:',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.w600,
                      color:
                          themeProvider.isDarkMode
                              ? Colors.white
                              : const Color(0xFF1F2937),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      final userEmail =
                          authProvider.firebaseUser?.email ?? 'غير مسجل';
                      return Column(
                        children: [
                          Text(
                            'البريد الإلكتروني الحالي: $userEmail',
                            style: GoogleFonts.cairo(
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF94A3B8)
                                      : const Color(0xFF6B7280),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'البريد المطلوب: <EMAIL>',
                            style: GoogleFonts.cairo(
                              color: const Color(0xFF10B981),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToAddPDF() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddPDFScreen()),
    );
  }

  void _navigateToManagePDFs() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ManagePDFsScreen()),
    );
  }

  void _navigateToSubjectPDFs(
    Subject subject,
    AcademicYear year,
    Semester semester,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ManagePDFsScreen(
              subjectId: subject.id,
              yearId: year.id,
              semesterId: semester.id,
              subjectName: subject.arabicName,
            ),
      ),
    );
  }
}
