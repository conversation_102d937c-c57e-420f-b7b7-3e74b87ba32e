@echo off
echo 🦋 تنظيف Flutter شامل - الحفاظ على legal2025 و اذكاري
echo =========================================================
echo.

echo المشاريع المحمية:
echo   ✅ legal2025 (D:\20223\2025\legl92025)
echo   ✅ اذكاري (جميع المواقع)
echo.
pause

echo 🧹 بدء تنظيف Flutter...
echo.

REM تنظيف Flutter cache العام
echo 1. تنظيف Flutter cache العام...
flutter clean
flutter pub cache repair

REM تنظيف Pub cache
echo 2. تنظيف Pub cache...
if exist "%LOCALAPPDATA%\Pub\Cache" (
    echo   حذف Pub Cache hosted packages...
    rd /s /q "%LOCALAPPDATA%\Pub\Cache\hosted" 2>nul
)

if exist "%USERPROFILE%\.pub-cache" (
    echo   حذف .pub-cache hosted packages...
    rd /s /q "%USERPROFILE%\.pub-cache\hosted" 2>nul
)

REM البحث عن مشاريع Flutter وتنظيفها
echo 3. البحث عن مشاريع Flutter وتنظيفها...

REM تنظيف المشروع الحالي (legal2025)
echo   تنظيف مشروع legal2025...
cd /d "D:\20223\2025\legl92025"
if exist "pubspec.yaml" (
    echo     - flutter clean في legal2025
    flutter clean
    if exist "build" rd /s /q "build" 2>nul
    if exist ".dart_tool" rd /s /q ".dart_tool" 2>nul
    if exist "android\app\build" rd /s /q "android\app\build" 2>nul
    if exist "android\build" rd /s /q "android\build" 2>nul
    if exist "ios\build" rd /s /q "ios\build" 2>nul
    if exist "web\build" rd /s /q "web\build" 2>nul
    echo     ✅ تم تنظيف legal2025 (محمي)
)

REM البحث عن مشروع اذكاري وتنظيفه
echo   البحث عن مشروع اذكاري...
for /d %%i in ("C:\*اذكاري*" "D:\*اذكاري*" "C:\Users\<USER>\*اذكاري*" "C:\Users\<USER>\Documents\*اذكاري*" "C:\Users\<USER>\Desktop\*اذكاري*") do (
    if exist "%%i\pubspec.yaml" (
        echo     وجد مشروع اذكاري: %%i
        cd /d "%%i"
        flutter clean
        if exist "build" rd /s /q "build" 2>nul
        if exist ".dart_tool" rd /s /q ".dart_tool" 2>nul
        if exist "android\app\build" rd /s /q "android\app\build" 2>nul
        if exist "android\build" rd /s /q "android\build" 2>nul
        if exist "ios\build" rd /s /q "ios\build" 2>nul
        if exist "web\build" rd /s /q "web\build" 2>nul
        echo     ✅ تم تنظيف اذكاري (محمي)
    )
)

REM حذف مشاريع Flutter الأخرى
echo 4. حذف مشاريع Flutter القديمة...

REM فحص مجلدات شائعة
for %%folder in ("C:\Users\<USER>\Documents" "C:\Users\<USER>\Desktop" "C:\Projects" "C:\Development" "C:\Code" "C:\Flutter" "C:\Apps") do (
    if exist %%folder (
        echo   فحص %%folder...
        for /d %%i in ("%%folder\*") do (
            if exist "%%i\pubspec.yaml" (
                echo     وجد مشروع Flutter: %%~ni
                echo %%~ni | findstr /i "legal2025 اذكاري" >nul
                if errorlevel 1 (
                    echo       ❌ حذف مشروع: %%i
                    rd /s /q "%%i" 2>nul
                ) else (
                    echo       ✅ محمي: %%i
                )
            )
        )
    )
)

REM تنظيف Android SDK cache
echo 5. تنظيف Android SDK cache...
if exist "%LOCALAPPDATA%\Android\Sdk" (
    echo   تنظيف Android SDK temp files...
    if exist "%LOCALAPPDATA%\Android\Sdk\.temp" rd /s /q "%LOCALAPPDATA%\Android\Sdk\.temp" 2>nul
    if exist "%LOCALAPPDATA%\Android\Sdk\build-cache" rd /s /q "%LOCALAPPDATA%\Android\Sdk\build-cache" 2>nul
)

REM تنظيف Gradle cache
echo 6. تنظيف Gradle cache...
if exist "%USERPROFILE%\.gradle\caches" (
    echo   حذف Gradle caches...
    rd /s /q "%USERPROFILE%\.gradle\caches" 2>nul
)

REM تنظيف Android Studio cache
echo 7. تنظيف Android Studio cache...
for /d %%i in ("%LOCALAPPDATA%\Google\AndroidStudio*") do (
    if exist "%%i" (
        echo   تنظيف %%i...
        if exist "%%i\caches" rd /s /q "%%i\caches" 2>nul
        if exist "%%i\tmp" rd /s /q "%%i\tmp" 2>nul
        if exist "%%i\log" rd /s /q "%%i\log" 2>nul
    )
)

REM حذف مشاريع Android Studio القديمة
echo 8. حذف مشاريع Android Studio القديمة...
if exist "C:\Users\<USER>\AndroidStudioProjects" (
    for /d %%i in ("C:\Users\<USER>\AndroidStudioProjects\*") do (
        echo   وجد مشروع Android: %%~ni
        echo %%~ni | findstr /i "legal2025 اذكاري" >nul
        if errorlevel 1 (
            echo     ❌ حذف: %%i
            rd /s /q "%%i" 2>nul
        ) else (
            echo     ✅ محمي: %%i
        )
    )
)

REM تنظيف VS Code Flutter extensions cache
echo 9. تنظيف VS Code Flutter extensions cache...
if exist "%USERPROFILE%\.vscode\extensions" (
    for /d %%i in ("%USERPROFILE%\.vscode\extensions\dart-code.*") do (
        if exist "%%i\out" rd /s /q "%%i\out" 2>nul
    )
)

REM إعادة تثبيت Flutter packages للمشاريع المحمية
echo 10. إعادة تثبيت packages للمشاريع المحمية...

echo   إعادة تثبيت packages لـ legal2025...
cd /d "D:\20223\2025\legl92025"
if exist "pubspec.yaml" (
    flutter pub get
    echo     ✅ تم تثبيت packages لـ legal2025
)

REM إعادة تثبيت packages لمشروع اذكاري
for /d %%i in ("C:\*اذكاري*" "D:\*اذكاري*" "C:\Users\<USER>\*اذكاري*" "C:\Users\<USER>\Documents\*اذكاري*" "C:\Users\<USER>\Desktop\*اذكاري*") do (
    if exist "%%i\pubspec.yaml" (
        echo   إعادة تثبيت packages لـ اذكاري...
        cd /d "%%i"
        flutter pub get
        echo     ✅ تم تثبيت packages لـ اذكاري
    )
)

echo.
echo ✅ تم الانتهاء من تنظيف Flutter!
echo.

echo 📊 حالة المشاريع المحمية:
echo   ✅ legal2025: نظيف ومُحدث
echo   ✅ اذكاري: نظيف ومُحدث
echo.

echo 💡 تم حذف:
echo   ❌ جميع مشاريع Flutter القديمة
echo   ❌ Pub cache
echo   ❌ Gradle cache  
echo   ❌ Android SDK cache
echo   ❌ مجلدات build و .dart_tool
echo.

echo 🎉 Flutter نظيف ومُحسن!
echo.

REM العودة للمجلد الأصلي
cd /d "D:\20223\2025\legl92025"

pause
