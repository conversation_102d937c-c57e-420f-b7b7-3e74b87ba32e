import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/pdf_model.dart';

class NotificationService {
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  static const String notificationsCollection = 'notifications';
  static const String userTokensCollection = 'user_tokens';

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    try {
      // طلب أذونات الإشعارات
      await _requestPermissions();

      // تهيئة الإشعارات المحلية
      await _initializeLocalNotifications();

      // الحصول على FCM Token وحفظه
      await _saveUserToken();

      // معالجة الإشعارات
      _setupNotificationHandlers();

      if (kDebugMode) print('✅ تم تهيئة خدمة الإشعارات بنجاح');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تهيئة خدمة الإشعارات: $e');
    }
  }

  /// طلب أذونات الإشعارات
  static Future<void> _requestPermissions() async {
    final settings = await _messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (kDebugMode) {
      print('إذن الإشعارات: ${settings.authorizationStatus}');
    }
  }

  /// تهيئة الإشعارات المحلية
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // إنشاء قناة إشعارات للأندرويد
    const androidChannel = AndroidNotificationChannel(
      'pdf_updates',
      'تحديثات PDF',
      description: 'إشعارات عند إضافة أو تحديث ملفات PDF',
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification'),
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(androidChannel);
  }

  /// حفظ FCM Token للمستخدم
  static Future<void> _saveUserToken() async {
    try {
      final token = await _messaging.getToken();
      if (token != null) {
        await _firestore.collection(userTokensCollection).doc(token).set({
          'token': token,
          'platform': defaultTargetPlatform.name,
          'lastUpdated': FieldValue.serverTimestamp(),
          'isActive': true,
        });

        if (kDebugMode) {
          print('✅ تم حفظ FCM Token: ${token.substring(0, 20)}...');
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في حفظ FCM Token: $e');
    }
  }

  /// إعداد معالجات الإشعارات
  static void _setupNotificationHandlers() {
    // معالجة الإشعارات عندما يكون التطبيق مفتوحاً
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _showLocalNotification(message);
    });

    // معالجة النقر على الإشعارات
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      _handleNotificationTap(message);
    });

    // معالجة الإشعارات في الخلفية
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  /// عرض إشعار محلي
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'pdf_updates',
        'تحديثات PDF',
        channelDescription: 'إشعارات عند إضافة أو تحديث ملفات PDF',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
        sound: RawResourceAndroidNotificationSound('notification'),
        enableVibration: true,
        playSound: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'notification.aiff',
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch ~/ 1000,
        message.notification?.title ?? 'تحديث جديد',
        message.notification?.body ?? 'تم إضافة محتوى جديد',
        details,
        payload: message.data.toString(),
      );

      // تشغيل اهتزاز خفيف
      HapticFeedback.lightImpact();

      if (kDebugMode) print('✅ تم عرض الإشعار المحلي');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في عرض الإشعار المحلي: $e');
    }
  }

  /// معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    if (kDebugMode) print('تم النقر على الإشعار: ${response.payload}');
    // يمكن إضافة منطق التنقل هنا
  }

  /// معالجة النقر على إشعار Firebase
  static void _handleNotificationTap(RemoteMessage message) {
    if (kDebugMode) print('تم النقر على إشعار Firebase: ${message.data}');
    // يمكن إضافة منطق التنقل هنا
  }

  /// إرسال إشعار عن PDF جديد
  static Future<void> sendPDFNotification({
    required String title,
    required String body,
    required String yearName,
    required String subjectName,
    required String pdfName,
    required String action, // 'added', 'updated', 'deleted'
  }) async {
    try {
      // حفظ الإشعار في قاعدة البيانات
      await _firestore.collection(notificationsCollection).add({
        'title': title,
        'body': body,
        'yearName': yearName,
        'subjectName': subjectName,
        'pdfName': pdfName,
        'action': action,
        'timestamp': FieldValue.serverTimestamp(),
        'type': 'pdf_update',
        'isRead': false,
      });

      // إرسال إشعار فوري لجميع المستخدمين
      await _sendToAllUsers(title, body, {
        'type': 'pdf_update',
        'action': action,
        'yearName': yearName,
        'subjectName': subjectName,
        'pdfName': pdfName,
      });

      if (kDebugMode) print('✅ تم إرسال إشعار PDF: $title');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إرسال إشعار PDF: $e');
    }
  }

  /// إرسال إشعار لجميع المستخدمين
  static Future<void> _sendToAllUsers(
    String title,
    String body,
    Map<String, dynamic> data,
  ) async {
    try {
      // الحصول على جميع tokens المستخدمين النشطين
      final tokensSnapshot =
          await _firestore
              .collection(userTokensCollection)
              .where('isActive', isEqualTo: true)
              .get();

      final tokens =
          tokensSnapshot.docs
              .map((doc) => doc.data()['token'] as String)
              .toList();

      if (tokens.isEmpty) {
        if (kDebugMode) print('لا توجد tokens للإرسال إليها');
        return;
      }

      // إرسال إشعار جماعي (يحتاج Cloud Functions)
      // للآن سنعرض في الكونسول
      if (kDebugMode) {
        print('🔔 إشعار جماعي:');
        print('العنوان: $title');
        print('المحتوى: $body');
        print('عدد المستقبلين: ${tokens.length}');
        print('البيانات: $data');
      }

      // يمكن إضافة منطق إرسال حقيقي هنا باستخدام Cloud Functions
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في الإرسال الجماعي: $e');
    }
  }

  /// الحصول على الإشعارات للمستخدم
  static Stream<QuerySnapshot> getNotificationsStream() {
    return _firestore
        .collection(notificationsCollection)
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots();
  }

  /// تحديد الإشعار كمقروء
  static Future<void> markAsRead(String notificationId) async {
    try {
      await _firestore
          .collection(notificationsCollection)
          .doc(notificationId)
          .update({'isRead': true});
    } catch (e) {
      if (kDebugMode) print('خطأ في تحديد الإشعار كمقروء: $e');
    }
  }

  /// حذف الإشعار
  static Future<void> deleteNotification(String notificationId) async {
    try {
      await _firestore
          .collection(notificationsCollection)
          .doc(notificationId)
          .delete();
    } catch (e) {
      if (kDebugMode) print('خطأ في حذف الإشعار: $e');
    }
  }

  /// إرسال إشعار عند إضافة ملف PDF جديد
  static Future<void> sendNewPDFNotification(PDFModel pdf) async {
    try {
      final notificationData = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'title': 'ملف جديد متاح 📄',
        'body': 'تم إضافة ملف جديد في ${pdf.category} - ${pdf.subjectName}',
        'type': 'new_pdf',
        'pdfId': pdf.id,
        'subjectId': pdf.subjectId,
        'category': pdf.category,
        'createdAt': FieldValue.serverTimestamp(),
        'isRead': false,
        'data': {
          'pdfName': pdf.name,
          'subjectName': pdf.subjectName,
          'category': pdf.category,
          'url': pdf.url,
        },
      };

      // حفظ الإشعار في قاعدة البيانات
      await _firestore
          .collection(notificationsCollection)
          .doc(notificationData['id'] as String)
          .set(notificationData);

      // إرسال الإشعار المحلي
      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch ~/ 1000,
        notificationData['title'] as String,
        notificationData['body'] as String,
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'pdf_updates',
            'تحديثات PDF',
            channelDescription: 'إشعارات عند إضافة أو تحديث ملفات PDF',
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
            color: Color(0xFF10B981),
          ),
        ),
        payload: pdf.id,
      );

      if (kDebugMode) {
        print('✅ تم إرسال إشعار ملف PDF جديد: ${pdf.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إرسال إشعار PDF: $e');
      }
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  static Stream<int> getUnreadNotificationsCount() {
    return _firestore
        .collection(notificationsCollection)
        .where('isRead', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  /// تمييز جميع الإشعارات كمقروءة
  static Future<void> markAllAsRead() async {
    try {
      final batch = _firestore.batch();
      final snapshot =
          await _firestore
              .collection(notificationsCollection)
              .where('isRead', isEqualTo: false)
              .get();

      for (var doc in snapshot.docs) {
        batch.update(doc.reference, {'isRead': true});
      }

      await batch.commit();

      if (kDebugMode) {
        print('✅ تم تمييز ${snapshot.docs.length} إشعار كمقروء');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تمييز جميع الإشعارات كمقروءة: $e');
      }
    }
  }
}

/// معالج الإشعارات في الخلفية
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  if (kDebugMode) {
    print('معالجة إشعار في الخلفية: ${message.messageId}');
  }
}
