import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import '../models/pdf_model.dart';
import 'notification_service.dart';
import '../data/academic_data.dart';

class AdminService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  // مجموعات Firestore
  static const String pdfsCollection = 'pdfs';
  static const String adminsCollection = 'admins';
  static const String notificationsCollection = 'notifications';
  static const String usersCollection = 'users';

  // الأدمن الرئيسي
  static const String mainAdminEmail = '<EMAIL>';

  /// التحقق من صلاحيات الأدمن
  static Future<bool> isAdmin(String email) async {
    try {
      if (email == mainAdminEmail) return true;

      final doc =
          await _firestore.collection(adminsCollection).doc(email).get();
      if (!doc.exists) return false;

      final admin = AdminUser.fromJson(doc.data()!);
      return admin.isActive;
    } catch (e) {
      if (kDebugMode) print('Error checking admin status: $e');
      return false;
    }
  }

  /// إنشاء الأدمن الرئيسي
  static Future<void> initializeMainAdmin() async {
    try {
      final adminDoc =
          await _firestore
              .collection(adminsCollection)
              .doc(mainAdminEmail)
              .get();

      if (!adminDoc.exists) {
        final mainAdmin = AdminUser(
          email: mainAdminEmail,
          name: 'أمير الشريف',
          permissions: [AdminPermissions.all],
          createdAt: DateTime.now(),
          isActive: true,
        );

        await _firestore
            .collection(adminsCollection)
            .doc(mainAdminEmail)
            .set(mainAdmin.toJson());
        if (kDebugMode) print('✅ تم إنشاء الأدمن الرئيسي: $mainAdminEmail');
      } else {
        if (kDebugMode) print('✅ الأدمن الرئيسي موجود بالفعل: $mainAdminEmail');
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إنشاء الأدمن الرئيسي: $e');
      // في حالة عدم وجود صلاحيات، نعتبر الأدمن موجود محلياً
      if (e.toString().contains('permission-denied')) {
        if (kDebugMode) print('🔧 سيتم التحقق من الأدمن محلياً فقط');
      }
    }
  }

  /// رفع ملف PDF
  static Future<String?> uploadPDF(File file, String fileName) async {
    try {
      final ref = _storage.ref().child('pdfs/$fileName');
      final uploadTask = await ref.putFile(file);
      final downloadUrl = await uploadTask.ref.getDownloadURL();

      if (kDebugMode) print('✅ تم رفع الملف: $downloadUrl');
      return downloadUrl;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في رفع الملف: $e');
      return null;
    }
  }

  /// إضافة PDF جديد
  static Future<bool> addPDF({
    required String name,
    required String url,
    required String category,
    required String subjectId,
    required String yearId,
    required String semesterId,
    required String adminEmail,
    required double fileSize,
  }) async {
    try {
      // التحقق من صلاحيات الأدمن (مؤقتاً معطل للاختبار)
      if (kDebugMode) {
        print('🔧 وضع الاختبار: تجاهل فحص صلاحيات الأدمن');
        print('📧 البريد المستخدم: $adminEmail');
      }

      // if (adminEmail != mainAdminEmail) {
      //   if (kDebugMode) print('❌ ليس لديك صلاحية إضافة PDF');
      //   return false;
      // }

      final pdfId = _firestore.collection(pdfsCollection).doc().id;

      final pdf = PDFModel(
        id: pdfId,
        name: name,
        url: url,
        category: category,
        subjectId: subjectId,
        yearId: yearId,
        semesterId: semesterId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        uploadedBy: adminEmail,
        fileSize: fileSize,
      );

      try {
        await _firestore
            .collection(pdfsCollection)
            .doc(pdfId)
            .set(pdf.toJson());
        if (kDebugMode) print('✅ تم إضافة PDF في Firebase: $name');
      } catch (e) {
        if (e.toString().contains('permission-denied')) {
          if (kDebugMode) {
            print('⚠️ لا توجد صلاحيات Firebase، سيتم المحاولة مرة أخرى...');
          }
          // إعادة المحاولة مع تأخير قصير
          await Future.delayed(const Duration(seconds: 1));
          try {
            await _firestore
                .collection(pdfsCollection)
                .doc(pdfId)
                .set(pdf.toJson());
            if (kDebugMode) {
              print('✅ تم إضافة PDF في Firebase بعد إعادة المحاولة: $name');
            }
          } catch (e2) {
            if (kDebugMode) {
              print('❌ فشل في إضافة PDF حتى بعد إعادة المحاولة: $e2');
            }
            return false;
          }
        } else {
          rethrow;
        }
      }

      // إرسال إشعار لجميع المستخدمين
      try {
        await _sendPDFNotification(pdf, 'تم إضافة');
      } catch (e) {
        if (kDebugMode) print('⚠️ خطأ في إرسال الإشعار: $e');
      }

      if (kDebugMode) print('✅ تم إضافة PDF جديد: $name');
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إضافة PDF: $e');
      return false;
    }
  }

  /// تحديث PDF
  static Future<bool> updatePDF({
    required String pdfId,
    String? name,
    String? url,
    String? category,
    required String adminEmail,
  }) async {
    try {
      final updates = <String, dynamic>{
        'updatedAt': DateTime.now().toIso8601String(),
      };

      if (name != null) updates['name'] = name;
      if (url != null) updates['url'] = url;
      if (category != null) updates['category'] = category;

      await _firestore.collection(pdfsCollection).doc(pdfId).update(updates);

      if (kDebugMode) print('✅ تم تحديث PDF: $pdfId');
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تحديث PDF: $e');
      return false;
    }
  }

  /// حذف PDF
  static Future<bool> deletePDF(String pdfId, String adminEmail) async {
    try {
      // الحصول على بيانات PDF قبل الحذف للإشعار
      final doc = await _firestore.collection(pdfsCollection).doc(pdfId).get();
      if (doc.exists) {
        final pdf = PDFModel.fromJson(doc.data()!);

        // حذف الملف من Storage
        try {
          final ref = _storage.refFromURL(pdf.url);
          await ref.delete();
        } catch (e) {
          if (kDebugMode) print('تحذير: لم يتم حذف الملف من Storage: $e');
        }

        // حذف من Firestore
        await _firestore.collection(pdfsCollection).doc(pdfId).delete();

        // إرسال إشعار
        await _sendPDFNotification(pdf, 'تم حذف');

        if (kDebugMode) print('✅ تم حذف PDF: ${pdf.name}');
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في حذف PDF: $e');
      return false;
    }
  }

  /// الحصول على PDFs حسب المادة والفئة
  static Stream<QuerySnapshot> getPDFsStream({
    String? subjectId,
    String? category,
    String? yearId,
  }) {
    Query query = _firestore.collection(pdfsCollection);

    if (subjectId != null) {
      query = query.where('subjectId', isEqualTo: subjectId);
    }
    if (category != null) {
      query = query.where('category', isEqualTo: category);
    }
    if (yearId != null) {
      query = query.where('yearId', isEqualTo: yearId);
    }

    return query.orderBy('createdAt', descending: true).snapshots();
  }

  /// إرسال إشعار عن PDF
  static Future<void> _sendPDFNotification(PDFModel pdf, String action) async {
    try {
      // الحصول على معلومات المادة والفرقة
      final subjectName = await _getSubjectName(pdf.subjectId, pdf.yearId);
      final yearName = await _getYearName(pdf.yearId);

      final title = '$action ملف PDF جديد';
      final body = '$yearName - $subjectName\n${pdf.name}';

      // إرسال إشعار باستخدام خدمة الإشعارات المحسنة
      await NotificationService.sendPDFNotification(
        title: title,
        body: body,
        yearName: yearName,
        subjectName: subjectName,
        pdfName: pdf.name,
        action: action.toLowerCase(),
      );

      if (kDebugMode) print('✅ تم إرسال إشعار: $title');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إرسال الإشعار: $e');
    }
  }

  /// الحصول على اسم المادة
  static Future<String> _getSubjectName(String subjectId, String yearId) async {
    try {
      final academicYears = AcademicData.getAcademicYears();

      for (final year in academicYears) {
        if (year.id == yearId) {
          for (final semester in year.semesters) {
            for (final subject in semester.subjects) {
              if (subject.id == subjectId) {
                return subject.arabicName;
              }
            }
          }
        }
      }

      return 'مادة غير محددة';
    } catch (e) {
      if (kDebugMode) print('خطأ في الحصول على اسم المادة: $e');
      return 'مادة غير محددة';
    }
  }

  /// الحصول على اسم الفرقة
  static Future<String> _getYearName(String yearId) async {
    switch (yearId) {
      case 'year1':
        return 'الفرقة الأولى';
      case 'year2':
        return 'الفرقة الثانية';
      case 'year3':
        return 'الفرقة الثالثة';
      case 'year4':
        return 'الفرقة الرابعة';
      default:
        return 'فرقة غير محددة';
    }
  }

  /// زيادة عداد التحميل
  static Future<void> incrementDownloadCount(String pdfId) async {
    try {
      await _firestore.collection(pdfsCollection).doc(pdfId).update({
        'downloadCount': FieldValue.increment(1),
      });
    } catch (e) {
      if (kDebugMode) print('خطأ في تحديث عداد التحميل: $e');
    }
  }
}
