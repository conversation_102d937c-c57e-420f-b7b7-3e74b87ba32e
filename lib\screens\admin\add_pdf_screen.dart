import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:file_picker/file_picker.dart';
import '../../providers/admin_provider.dart';
import '../../providers/theme_provider.dart';
import '../../data/academic_data.dart';
import '../../models/subject.dart';

class AddPDFScreen extends StatefulWidget {
  final String? preSelectedYearId;
  final String? preSelectedSemesterId;
  final String? preSelectedSubjectId;
  final String? preSelectedCategory;

  const AddPDFScreen({
    super.key,
    this.preSelectedYearId,
    this.preSelectedSemesterId,
    this.preSelectedSubjectId,
    this.preSelectedCategory,
  });

  @override
  State<AddPDFScreen> createState() => _AddPDFScreenState();
}

class _AddPDFScreenState extends State<AddPDFScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _urlController = TextEditingController();

  String? _selectedYearId;
  String? _selectedSemesterId;
  String? _selectedSubjectId;
  String? _selectedCategory;

  PlatformFile? _selectedFile;
  bool _isUploading = false;

  final List<String> _categories = [
    'أسئلة',
    'امتحانات',
    'ملخصات',
    'الكتاب الرسمي',
  ];

  @override
  void initState() {
    super.initState();
    _selectedYearId = widget.preSelectedYearId;
    _selectedSemesterId = widget.preSelectedSemesterId;
    _selectedSubjectId = widget.preSelectedSubjectId;
    _selectedCategory = widget.preSelectedCategory;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<AdminProvider, ThemeProvider>(
      builder: (context, adminProvider, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          appBar: _buildAppBar(themeProvider),
          body: _buildBody(themeProvider, adminProvider),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeProvider themeProvider) {
    return AppBar(
      backgroundColor:
          themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
      elevation: 0,
      title: Text(
        'إضافة ملف PDF جديد',
        style: GoogleFonts.cairo(
          fontWeight: FontWeight.w700,
          color:
              themeProvider.isDarkMode ? Colors.white : const Color(0xFF1F2937),
        ),
      ),
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color:
              themeProvider.isDarkMode ? Colors.white : const Color(0xFF1F2937),
        ),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildBody(ThemeProvider themeProvider, AdminProvider adminProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسائل النجاح والخطأ
            if (adminProvider.error != null)
              _buildErrorMessage(
                adminProvider.error!,
                themeProvider,
                adminProvider,
              ),
            if (adminProvider.success != null)
              _buildSuccessMessage(
                adminProvider.success!,
                themeProvider,
                adminProvider,
              ),

            // معلومات الملف
            _buildFileInfoCard(themeProvider),

            const SizedBox(height: 24),

            // اختيار الفرقة والمادة
            _buildSelectionCard(themeProvider),

            const SizedBox(height: 24),

            // رفع الملف أو إدخال الرابط
            _buildFileUploadCard(themeProvider),

            const SizedBox(height: 32),

            // زر الإضافة
            _buildAddButton(themeProvider, adminProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildFileInfoCard(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الملف',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color:
                  themeProvider.isDarkMode
                      ? Colors.white
                      : const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _nameController,
            decoration: InputDecoration(
              labelText: 'اسم الملف',
              hintText: 'مثال: امتحان الفصل الأول 2024',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.description),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال اسم الملف';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSelectionCard(ThemeProvider themeProvider) {
    final academicYears = AcademicData.getAcademicYears();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختيار الموقع',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color:
                  themeProvider.isDarkMode
                      ? Colors.white
                      : const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 16),

          // اختيار الفرقة
          DropdownButtonFormField<String>(
            value: _selectedYearId,
            decoration: InputDecoration(
              labelText: 'الفرقة الدراسية',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.school),
            ),
            items:
                academicYears.map((year) {
                  return DropdownMenuItem(
                    value: year.id,
                    child: Text(year.arabicName, style: GoogleFonts.cairo()),
                  );
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedYearId = value;
                _selectedSemesterId = null;
                _selectedSubjectId = null;
              });
            },
            validator: (value) => value == null ? 'يرجى اختيار الفرقة' : null,
          ),

          const SizedBox(height: 16),

          // اختيار الفصل الدراسي
          if (_selectedYearId != null) ...[
            DropdownButtonFormField<String>(
              value: _selectedSemesterId,
              decoration: InputDecoration(
                labelText: 'الفصل الدراسي',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: const Icon(Icons.calendar_today),
              ),
              items:
                  _getSelectedYear()?.semesters.map((semester) {
                    return DropdownMenuItem(
                      value: semester.id,
                      child: Text(
                        semester.arabicName,
                        style: GoogleFonts.cairo(),
                      ),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedSemesterId = value;
                  _selectedSubjectId = null;
                });
              },
              validator:
                  (value) => value == null ? 'يرجى اختيار الفصل الدراسي' : null,
            ),
            const SizedBox(height: 16),
          ],

          // اختيار المادة
          if (_selectedSemesterId != null) ...[
            DropdownButtonFormField<String>(
              value: _selectedSubjectId,
              decoration: InputDecoration(
                labelText: 'المادة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: const Icon(Icons.book),
              ),
              items:
                  _getSelectedSemester()?.subjects.map((subject) {
                    return DropdownMenuItem(
                      value: subject.id,
                      child: Text(
                        subject.arabicName,
                        style: GoogleFonts.cairo(),
                      ),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedSubjectId = value;
                });
              },
              validator: (value) => value == null ? 'يرجى اختيار المادة' : null,
            ),
            const SizedBox(height: 16),
          ],

          // اختيار القسم
          if (_selectedSubjectId != null) ...[
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: InputDecoration(
                labelText: 'القسم',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: const Icon(Icons.category),
              ),
              items:
                  _categories.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(category, style: GoogleFonts.cairo()),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value;
                });
              },
              validator: (value) => value == null ? 'يرجى اختيار القسم' : null,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFileUploadCard(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'رفع الملف',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.w700,
              fontSize: 18,
              color:
                  themeProvider.isDarkMode
                      ? Colors.white
                      : const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 16),

          // زر رفع الملف
          Container(
            width: double.infinity,
            height: 120,
            decoration: BoxDecoration(
              border: Border.all(
                color:
                    _selectedFile != null
                        ? const Color(0xFF10B981)
                        : Colors.grey.withValues(alpha: 0.3),
                width: 2,
                style: BorderStyle.solid,
              ),
              borderRadius: BorderRadius.circular(12),
              color:
                  _selectedFile != null
                      ? const Color(0xFF10B981).withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.05),
            ),
            child: InkWell(
              onTap: _pickFile,
              borderRadius: BorderRadius.circular(12),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _selectedFile != null
                        ? Icons.check_circle
                        : Icons.cloud_upload,
                    size: 40,
                    color:
                        _selectedFile != null
                            ? const Color(0xFF10B981)
                            : Colors.grey,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _selectedFile != null
                        ? 'تم اختيار: ${_selectedFile!.name}'
                        : 'اضغط لاختيار ملف PDF',
                    style: GoogleFonts.cairo(
                      color:
                          _selectedFile != null
                              ? const Color(0xFF10B981)
                              : Colors.grey,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // أو إدخال رابط مباشر
          Text(
            'أو أدخل رابط مباشر:',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.w600,
              color:
                  themeProvider.isDarkMode
                      ? Colors.white
                      : const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _urlController,
            decoration: InputDecoration(
              labelText: 'رابط الملف',
              hintText: 'https://example.com/file.pdf',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.link),
            ),
            validator: (value) {
              if (_selectedFile == null && (value == null || value.isEmpty)) {
                return 'يرجى رفع ملف أو إدخال رابط';
              }
              if (value != null &&
                  value.isNotEmpty &&
                  !Uri.tryParse(value)!.isAbsolute) {
                return 'يرجى إدخال رابط صحيح';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAddButton(
    ThemeProvider themeProvider,
    AdminProvider adminProvider,
  ) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: (_isUploading || adminProvider.isLoading) ? null : _addPDF,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF6366F1),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
        child:
            (_isUploading || adminProvider.isLoading)
                ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
                : Text(
                  'إضافة الملف',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                ),
      ),
    );
  }

  Widget _buildErrorMessage(
    String message,
    ThemeProvider themeProvider,
    AdminProvider adminProvider,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFEF4444).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFEF4444).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.error, color: Color(0xFFEF4444)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: GoogleFonts.cairo(color: const Color(0xFFEF4444)),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Color(0xFFEF4444)),
            onPressed: () => adminProvider.clearMessages(),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessMessage(
    String message,
    ThemeProvider themeProvider,
    AdminProvider adminProvider,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF10B981).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF10B981).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.check_circle, color: Color(0xFF10B981)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: GoogleFonts.cairo(color: const Color(0xFF10B981)),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Color(0xFF10B981)),
            onPressed: () => adminProvider.clearMessages(),
          ),
        ],
      ),
    );
  }

  AcademicYear? _getSelectedYear() {
    if (_selectedYearId == null) return null;
    return AcademicData.getAcademicYears().firstWhere(
      (year) => year.id == _selectedYearId,
    );
  }

  Semester? _getSelectedSemester() {
    final year = _getSelectedYear();
    if (year == null || _selectedSemesterId == null) return null;
    return year.semesters.firstWhere(
      (semester) => semester.id == _selectedSemesterId,
    );
  }

  Future<void> _pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedFile = result.files.first;
          _urlController.clear(); // مسح الرابط إذا تم اختيار ملف
        });
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في اختيار الملف: $e');
    }
  }

  Future<void> _addPDF() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isUploading = true;
    });

    try {
      final adminProvider = context.read<AdminProvider>();
      String? fileUrl;

      // رفع الملف إذا تم اختياره
      if (_selectedFile != null) {
        // TODO: رفع الملف إلى Firebase Storage
        // fileUrl = await adminProvider.uploadPDF(File(_selectedFile!.path!), _selectedFile!.name);
        fileUrl = 'https://example.com/${_selectedFile!.name}'; // مؤقت للاختبار
      } else {
        fileUrl = _urlController.text;
      }

      if (fileUrl.isNotEmpty) {
        final success = await adminProvider.addPDF(
          name: _nameController.text,
          url: fileUrl,
          category: _selectedCategory!,
          subjectId: _selectedSubjectId!,
          yearId: _selectedYearId!,
          semesterId: _selectedSemesterId!,
          fileSize: _selectedFile?.size.toDouble() ?? 0.0,
        );

        if (success) {
          // مسح النموذج
          _nameController.clear();
          _urlController.clear();
          setState(() {
            _selectedFile = null;
          });

          // العودة للصفحة السابقة بعد 2 ثانية
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) Navigator.pop(context);
          });
        }
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في إضافة PDF: $e');
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _urlController.dispose();
    super.dispose();
  }
}
