@echo off
echo 🧹 تنظيف قرص C - الحفاظ على legal2025 و اذكاري
echo ================================================
echo.

echo ⚠️  تحذير: سيتم حذف المشاريع القديمة والملفات المؤقتة
echo المشاريع المحمية: legal2025, اذكاري
echo.
pause

echo 📊 فحص مساحة القرص قبل التنظيف...
dir C:\ | find "bytes free"
echo.

echo 🗑️  بدء عملية التنظيف...
echo.

REM تنظيف ملفات Windows المؤقتة
echo 1. تنظيف ملفات Windows المؤقتة...
del /q /f /s "%TEMP%\*" 2>nul
del /q /f /s "C:\Windows\Temp\*" 2>nul
del /q /f /s "C:\Windows\Prefetch\*" 2>nul
del /q /f /s "C:\Windows\SoftwareDistribution\Download\*" 2>nul

REM تنظيف سلة المحذوفات
echo 2. تنظيف سلة المحذوفات...
rd /s /q "C:\$Recycle.Bin" 2>nul

REM تنظيف ملفات المتصفحات
echo 3. تنظيف ملفات المتصفحات...
del /q /f /s "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache\*" 2>nul
del /q /f /s "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache\*" 2>nul
del /q /f /s "%APPDATA%\Mozilla\Firefox\Profiles\*\cache2\*" 2>nul

REM تنظيف Flutter cache (عام)
echo 4. تنظيف Flutter cache العام...
if exist "%LOCALAPPDATA%\Pub\Cache" (
    echo    - تنظيف Pub Cache...
    rd /s /q "%LOCALAPPDATA%\Pub\Cache\hosted" 2>nul
)

REM تنظيف Android SDK cache
echo 5. تنظيف Android SDK cache...
if exist "%LOCALAPPDATA%\Android\Sdk\.temp" (
    rd /s /q "%LOCALAPPDATA%\Android\Sdk\.temp" 2>nul
)

REM تنظيف Visual Studio cache
echo 6. تنظيف Visual Studio cache...
if exist "%LOCALAPPDATA%\Microsoft\VisualStudio" (
    for /d %%i in ("%LOCALAPPDATA%\Microsoft\VisualStudio\*") do (
        if exist "%%i\ComponentModelCache" rd /s /q "%%i\ComponentModelCache" 2>nul
        if exist "%%i\Extensions" rd /s /q "%%i\Extensions" 2>nul
    )
)

REM البحث عن مشاريع Flutter القديمة وحذفها (مع الحماية)
echo 7. البحث عن مشاريع Flutter القديمة...
echo    البحث في المجلدات الشائعة...

REM فحص مجلد المستخدم
if exist "C:\Users\<USER>\Documents" (
    echo    - فحص Documents...
    for /d %%i in ("C:\Users\<USER>\Documents\*") do (
        if exist "%%i\pubspec.yaml" (
            echo      وجد مشروع Flutter: %%~ni
            echo %%~ni | findstr /i "legal2025 اذكاري" >nul
            if errorlevel 1 (
                echo      ❌ حذف: %%i
                rd /s /q "%%i" 2>nul
            ) else (
                echo      ✅ محمي: %%i
            )
        )
    )
)

REM فحص مجلد Desktop
if exist "C:\Users\<USER>\Desktop" (
    echo    - فحص Desktop...
    for /d %%i in ("C:\Users\<USER>\Desktop\*") do (
        if exist "%%i\pubspec.yaml" (
            echo      وجد مشروع Flutter: %%~ni
            echo %%~ni | findstr /i "legal2025 اذكاري" >nul
            if errorlevel 1 (
                echo      ❌ حذف: %%i
                rd /s /q "%%i" 2>nul
            ) else (
                echo      ✅ محمي: %%i
            )
        )
    )
)

REM فحص مجلدات التطوير الشائعة
for %%folder in ("C:\Projects" "C:\Development" "C:\Code" "C:\Flutter" "C:\Apps") do (
    if exist %%folder (
        echo    - فحص %%folder...
        for /d %%i in ("%%folder\*") do (
            if exist "%%i\pubspec.yaml" (
                echo      وجد مشروع Flutter: %%~ni
                echo %%~ni | findstr /i "legal2025 اذكاري" >nul
                if errorlevel 1 (
                    echo      ❌ حذف: %%i
                    rd /s /q "%%i" 2>nul
                ) else (
                    echo      ✅ محمي: %%i
                )
            )
        )
    )
)

REM حذف مجلدات build في المشاريع المحمية
echo 8. تنظيف مجلدات build في المشاريع المحمية...
for /d /r "D:\20223\2025\legl92025" %%i in (build) do (
    if exist "%%i" (
        echo    - حذف: %%i
        rd /s /q "%%i" 2>nul
    )
)

REM البحث عن مشروع اذكاري وتنظيفه
echo    البحث عن مشروع اذكاري...
for /d %%i in ("C:\*اذكاري*" "D:\*اذكاري*" "C:\Users\<USER>\*اذكاري*") do (
    if exist "%%i" (
        echo    وجد مشروع اذكاري: %%i
        if exist "%%i\build" (
            echo      - تنظيف build folder
            rd /s /q "%%i\build" 2>nul
        )
        if exist "%%i\.dart_tool" (
            echo      - تنظيف .dart_tool
            rd /s /q "%%i\.dart_tool" 2>nul
        )
    )
)

REM تنظيف ملفات النظام الأخرى
echo 9. تنظيف ملفات النظام الأخرى...
del /q /f "C:\Windows\*.log" 2>nul
del /q /f "C:\Windows\*.tmp" 2>nul
del /q /f "C:\*.tmp" 2>nul
del /q /f "C:\*.temp" 2>nul

REM تنظيف Event Logs
echo 10. تنظيف Event Logs...
for /f "tokens=*" %%i in ('wevtutil el') do (
    wevtutil cl "%%i" 2>nul
)

REM تنظيف IIS Logs (إذا كان موجود)
echo 11. تنظيف IIS Logs...
if exist "C:\inetpub\logs" (
    del /q /f /s "C:\inetpub\logs\*" 2>nul
)

REM تشغيل Disk Cleanup
echo 12. تشغيل أداة تنظيف القرص...
cleanmgr /sagerun:1

echo.
echo ✅ تم الانتهاء من التنظيف!
echo.

echo 📊 فحص مساحة القرص بعد التنظيف...
dir C:\ | find "bytes free"
echo.

echo 🎉 تم توفير مساحة إضافية!
echo المشاريع المحمية:
echo   ✅ legal2025 (D:\20223\2025\legl92025)
echo   ✅ اذكاري (محمي في جميع المواقع)
echo.

echo 💡 نصائح إضافية:
echo   - قم بإعادة تشغيل الكمبيوتر لتحرير المزيد من الذاكرة
echo   - استخدم أداة Disk Cleanup بانتظام
echo   - احذف البرامج غير المستخدمة من Control Panel
echo.

pause
