@echo off
title تنظيف شامل لقرص C - الحفاظ على legal2025 و اذكاري
color 0A

echo.
echo  ██████╗██╗     ███████╗ █████╗ ███╗   ██╗██╗   ██╗██████╗ 
echo ██╔════╝██║     ██╔════╝██╔══██╗████╗  ██║██║   ██║██╔══██╗
echo ██║     ██║     █████╗  ███████║██╔██╗ ██║██║   ██║██████╔╝
echo ██║     ██║     ██╔══╝  ██╔══██║██║╚██╗██║██║   ██║██╔═══╝ 
echo ╚██████╗███████╗███████╗██║  ██║██║ ╚████║╚██████╔╝██║     
echo  ╚═════╝╚══════╝╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ╚═════╝ ╚═╝     
echo.
echo 🧹 تنظيف شامل لقرص C - الحفاظ على المشاريع المهمة
echo ================================================
echo.

echo 🛡️  المشاريع المحمية:
echo   ✅ legal2025 (D:\20223\2025\legl92025)
echo   ✅ اذكاري (جميع المواقع)
echo.

echo ⚠️  سيتم حذف:
echo   ❌ جميع مشاريع Flutter/Android القديمة
echo   ❌ ملفات Windows المؤقتة
echo   ❌ cache المتصفحات
echo   ❌ مجلدات build و node_modules
echo   ❌ ملفات APK/ISO كبيرة
echo   ❌ سلة المحذوفات
echo   ❌ ملفات التطوير القديمة
echo.

echo 📊 فحص مساحة القرص قبل التنظيف...
for /f "tokens=3" %%a in ('dir C:\ ^| find "bytes free"') do set BEFORE=%%a
echo المساحة المتاحة قبل التنظيف: %BEFORE% bytes
echo.

set /p confirm="هل تريد المتابعة؟ (Y/N): "
if /i not "%confirm%"=="Y" goto :end

echo.
echo 🚀 بدء عملية التنظيف الشاملة...
echo ================================
echo.

REM إنشاء مجلد للتقارير
if not exist "cleanup_reports" mkdir cleanup_reports
set REPORT=cleanup_reports\cleanup_%date:~-4%%date:~3,2%%date:~0,2%_%time:~0,2%%time:~3,2%.txt

echo تقرير التنظيف الشامل - %date% %time% > %REPORT%
echo ================================================ >> %REPORT%
echo المشاريع المحمية: legal2025, اذكاري >> %REPORT%
echo. >> %REPORT%

echo 📋 التقرير سيُحفظ في: %REPORT%
echo.

REM المرحلة 1: تنظيف Flutter
echo 🦋 المرحلة 1: تنظيف Flutter...
echo ==============================
echo المرحلة 1: تنظيف Flutter >> %REPORT%
call flutter_cleanup.bat >> %REPORT% 2>&1
echo ✅ انتهت المرحلة 1
echo.

REM المرحلة 2: تنظيف النظام الأساسي
echo 🗑️  المرحلة 2: تنظيف النظام...
echo ==============================
echo المرحلة 2: تنظيف النظام >> %REPORT%

echo   - تنظيف ملفات Windows المؤقتة...
del /q /f /s "%TEMP%\*" 2>nul
del /q /f /s "C:\Windows\Temp\*" 2>nul
del /q /f /s "C:\Windows\Prefetch\*" 2>nul
echo     ✅ تم تنظيف ملفات Windows المؤقتة

echo   - تنظيف سلة المحذوفات...
rd /s /q "C:\$Recycle.Bin" 2>nul
echo     ✅ تم تنظيف سلة المحذوفات

echo   - تنظيف cache المتصفحات...
taskkill /f /im chrome.exe 2>nul
taskkill /f /im msedge.exe 2>nul
del /q /f /s "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache\*" 2>nul
del /q /f /s "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache\*" 2>nul
echo     ✅ تم تنظيف cache المتصفحات

echo ✅ انتهت المرحلة 2
echo.

REM المرحلة 3: تنظيف متقدم
echo 🚀 المرحلة 3: تنظيف متقدم...
echo ==============================
echo المرحلة 3: تنظيف متقدم >> %REPORT%
call advanced_cleanup.bat >> %REPORT% 2>&1
echo ✅ انتهت المرحلة 3
echo.

REM المرحلة 4: تنظيف إضافي
echo 🔧 المرحلة 4: تنظيف إضافي...
echo ==============================
echo المرحلة 4: تنظيف إضافي >> %REPORT%

echo   - تنظيف Event Logs...
for /f "tokens=*" %%i in ('wevtutil el 2^>nul') do (
    wevtutil cl "%%i" 2>nul
)
echo     ✅ تم تنظيف Event Logs

echo   - تنظيف IIS Logs...
if exist "C:\inetpub\logs" (
    del /q /f /s "C:\inetpub\logs\*" 2>nul
    echo     ✅ تم تنظيف IIS Logs
)

echo   - تنظيف System Files...
del /q /f "C:\Windows\*.log" 2>nul
del /q /f "C:\Windows\*.tmp" 2>nul
del /q /f "C:\*.tmp" 2>nul
del /q /f "C:\*.temp" 2>nul
echo     ✅ تم تنظيف System Files

echo ✅ انتهت المرحلة 4
echo.

REM المرحلة 5: تشغيل أدوات النظام
echo 🛠️  المرحلة 5: أدوات النظام...
echo ==============================
echo المرحلة 5: أدوات النظام >> %REPORT%

echo   - تشغيل Disk Cleanup...
cleanmgr /sagerun:1
echo     ✅ تم تشغيل Disk Cleanup

echo ✅ انتهت المرحلة 5
echo.

REM حساب المساحة المحررة
echo 📊 حساب النتائج...
for /f "tokens=3" %%a in ('dir C:\ ^| find "bytes free"') do set AFTER=%%a
echo المساحة المتاحة بعد التنظيف: %AFTER% bytes

echo.
echo النتائج النهائية >> %REPORT%
echo =============== >> %REPORT%
echo المساحة قبل التنظيف: %BEFORE% bytes >> %REPORT%
echo المساحة بعد التنظيف: %AFTER% bytes >> %REPORT%
echo. >> %REPORT%

echo ✅✅✅ تم الانتهاء من التنظيف الشامل! ✅✅✅
echo ============================================
echo.

echo 📊 النتائج:
echo   📈 المساحة قبل التنظيف: %BEFORE% bytes
echo   📈 المساحة بعد التنظيف: %AFTER% bytes
echo   📋 التقرير الكامل: %REPORT%
echo.

echo 🛡️  المشاريع المحمية (لم تُحذف):
echo   ✅ legal2025 - نظيف ومُحدث
echo   ✅ اذكاري - نظيف ومُحدث
echo.

echo 🎉 تم توفير مساحة كبيرة!
echo.

echo 💡 خطوات إضافية موصى بها:
echo   1. إعادة تشغيل الكمبيوتر
echo   2. تشغيل: chkdsk C: /f
echo   3. تشغيل: sfc /scannow
echo   4. Defragment القرص
echo   5. تحديث Windows
echo.

echo 🚀 الآن يمكنك تشغيل مشروع legal2025 بدون مشاكل!
echo.

:end
pause
