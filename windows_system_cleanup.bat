@echo off
title تنظيف ملفات Windows النظام - تحرير مساحة كبيرة
color 0B

echo.
echo 🪟 تنظيف ملفات Windows النظام - تحرير مساحة كبيرة
echo ================================================
echo.

echo ⚠️  سيتم حذف:
echo   ❌ ملفات Windows Update القديمة
echo   ❌ ملفات System Restore القديمة
echo   ❌ ملفات Hibernation
echo   ❌ ملفات Page File القديمة
echo   ❌ ملفات Windows Logs
echo   ❌ ملفات Driver Store القديمة
echo   ❌ ملفات WinSxS القديمة
echo.

echo 📊 فحص مساحة القرص قبل التنظيف...
for /f "tokens=3" %%a in ('dir C:\ ^| find "bytes free"') do set BEFORE=%%a
echo المساحة المتاحة: %BEFORE% bytes
echo.

set /p confirm="هل تريد المتابعة؟ (Y/N): "
if /i not "%confirm%"=="Y" goto :end

echo.
echo 🧹 بدء تنظيف ملفات Windows النظام...
echo ===================================
echo.

REM تشغيل بصلاحيات المدير
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ⚠️  يتطلب صلاحيات المدير. إعادة تشغيل...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo 1. تنظيف Windows Update files...
dism /online /cleanup-image /startcomponentcleanup /resetbase
echo   ✅ تم تنظيف Windows Update files

echo 2. تنظيف WinSxS folder...
dism /online /cleanup-image /spsuperseded
echo   ✅ تم تنظيف WinSxS folder

echo 3. حذف ملفات Windows Update cache...
net stop wuauserv
net stop cryptSvc
net stop bits
net stop msiserver
rd /s /q "C:\Windows\SoftwareDistribution"
rd /s /q "C:\Windows\System32\catroot2"
net start wuauserv
net start cryptSvc
net start bits
net start msiserver
echo   ✅ تم حذف Windows Update cache

echo 4. تنظيف System Restore points القديمة...
vssadmin delete shadows /for=c: /oldest /quiet
echo   ✅ تم تنظيف System Restore points

echo 5. تعطيل وحذف Hibernation file...
powercfg -h off
if exist "C:\hiberfil.sys" del /f /q "C:\hiberfil.sys"
echo   ✅ تم حذف Hibernation file

echo 6. تنظيف Page File...
echo تعديل إعدادات Page File...
wmic computersystem where name="%computername%" set AutomaticManagedPagefile=False
wmic pagefileset where name="C:\\pagefile.sys" set InitialSize=512,MaximumSize=1024
echo   ✅ تم تحسين Page File

echo 7. حذف Windows Logs...
for /f "tokens=*" %%i in ('wevtutil el') do (
    wevtutil cl "%%i" 2>nul
)
rd /s /q "C:\Windows\Logs" 2>nul
md "C:\Windows\Logs"
echo   ✅ تم حذف Windows Logs

echo 8. تنظيف Driver Store...
pnputil /delete-driver oem*.inf /uninstall /force 2>nul
echo   ✅ تم تنظيف Driver Store

echo 9. حذف Windows Error Reporting files...
rd /s /q "C:\ProgramData\Microsoft\Windows\WER" 2>nul
md "C:\ProgramData\Microsoft\Windows\WER"
echo   ✅ تم حذف Windows Error Reporting files

echo 10. تنظيف Windows Defender...
rd /s /q "C:\ProgramData\Microsoft\Windows Defender\Scans\History" 2>nul
md "C:\ProgramData\Microsoft\Windows Defender\Scans\History"
echo   ✅ تم تنظيف Windows Defender

echo 11. حذف Temporary Internet Files...
rd /s /q "C:\Windows\Temp" 2>nul
md "C:\Windows\Temp"
rd /s /q "%TEMP%" 2>nul
md "%TEMP%"
echo   ✅ تم حذف Temporary Internet Files

echo 12. تنظيف Windows Search Index...
net stop wsearch
rd /s /q "C:\ProgramData\Microsoft\Search\Data" 2>nul
net start wsearch
echo   ✅ تم تنظيف Windows Search Index

echo 13. حذف Windows Media Player cache...
rd /s /q "%LOCALAPPDATA%\Microsoft\Media Player" 2>nul
echo   ✅ تم حذف Windows Media Player cache

echo 14. تنظيف Windows Store cache...
wsreset.exe
echo   ✅ تم تنظيف Windows Store cache

echo 15. حذف Thumbnail cache...
rd /s /q "%LOCALAPPDATA%\Microsoft\Windows\Explorer" 2>nul
echo   ✅ تم حذف Thumbnail cache

echo 16. تنظيف Font cache...
net stop FontCache
rd /s /q "C:\Windows\ServiceProfiles\LocalService\AppData\Local\FontCache" 2>nul
net start FontCache
echo   ✅ تم تنظيف Font cache

echo 17. حذف Windows Update Medic Service logs...
rd /s /q "C:\Windows\Logs\WindowsUpdate" 2>nul
echo   ✅ تم حذف Windows Update logs

echo 18. تنظيف CBS logs...
del /f /q "C:\Windows\Logs\CBS\*.log" 2>nul
echo   ✅ تم تنظيف CBS logs

echo 19. حذف DISM logs...
del /f /q "C:\Windows\Logs\DISM\*.log" 2>nul
echo   ✅ تم حذف DISM logs

echo 20. تشغيل Disk Cleanup مع جميع الخيارات...
cleanmgr /sagerun:1
echo   ✅ تم تشغيل Disk Cleanup

echo 21. تنظيف Registry (أساسي)...
reg delete "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\RecentDocs" /f 2>nul
reg delete "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\RecentDocs" /f 2>nul
echo   ✅ تم تنظيف Registry

echo 22. إعادة تشغيل خدمات Windows...
net stop spooler
net start spooler
net stop themes
net start themes
echo   ✅ تم إعادة تشغيل الخدمات

echo.
echo 📊 حساب النتائج...
for /f "tokens=3" %%a in ('dir C:\ ^| find "bytes free"') do set AFTER=%%a

echo.
echo ✅✅✅ تم الانتهاء من تنظيف Windows النظام! ✅✅✅
echo ===============================================
echo.

echo 📊 النتائج:
echo   📈 المساحة قبل التنظيف: %BEFORE% bytes
echo   📈 المساحة بعد التنظيف: %AFTER% bytes
echo.

echo 🎉 تم تحرير مساحة كبيرة من ملفات Windows!
echo.

echo 💡 خطوات إضافية موصى بها:
echo   1. إعادة تشغيل الكمبيوتر فوراً
echo   2. تشغيل: sfc /scannow
echo   3. تشغيل: chkdsk C: /f /r
echo   4. تحديث Windows
echo   5. إلغاء تثبيت البرامج غير المستخدمة
echo.

echo 🚀 الآن يجب أن تكون المساحة كافية!
echo.

:end
pause
