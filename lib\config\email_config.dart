/// إعدادات البريد الإلكتروني لإرسال أكواد التحقق
class EmailConfig {
  // إعدادات البريد الإلكتروني - حساب حقيقي للاختبار
  static const String senderEmail = '<EMAIL>';
  static const String senderName = 'تطبيق Legal 2025';

  // كلمة مرور التطبيق الحقيقية (تم إنشاؤها من Gmail)
  static const String appPassword = 'qwer tyui asdf ghjk';

  // 📧 خطوات إعداد Gmail لإرسال البريد الإلكتروني:
  //
  // 1. إنشاء حساب Gmail جديد أو استخدام حساب موجود
  // 2. تفعيل التحقق بخطوتين (2-Factor Authentication):
  //    - اذهب إلى: https://myaccount.google.com/security
  //    - اختر "2-Step Verification" وقم بتفعيله
  //
  // 3. إنشاء كلمة مرور التطبيق (App Password):
  //    - في نفس صفحة الأمان، اختر "App passwords"
  //    - اختر "Mail" كنوع التطبيق
  //    - انسخ كلمة المرور المُنشأة (16 حرف)
  //    - استبدل 'your_app_password_here' بكلمة المرور الحقيقية
  //
  // 4. تحديث البريد الإلكتروني:
  //    - غير '<EMAIL>' إلى بريدك الإلكتروني
  //
  // 🔒 ملاحظات أمنية مهمة:
  // - لا تشارك كلمة مرور التطبيق مع أحد
  // - في بيئة الإنتاج، استخدم متغيرات البيئة أو Firebase Remote Config
  // - تأكد من عدم رفع كلمة المرور إلى Git

  // 🚀 بدائل أخرى للإرسال:
  // - SendGrid API (مجاني حتى 100 بريد يومياً)
  // - Firebase Functions مع Nodemailer
  // - Amazon SES
  // - Mailgun
  // - Twilio SendGrid

  // 🧪 للاختبار السريع:
  // يمكنك استخدام خدمة Mailtrap أو Ethereal Email للاختبار
  // بدون إرسال بريد إلكتروني حقيقي

  /// التحقق من صحة الإعدادات
  static bool get isConfigured {
    return senderEmail.isNotEmpty &&
        senderEmail.contains('@') &&
        appPassword != 'your_app_password_here' &&
        appPassword.isNotEmpty;
  }

  /// رسالة تحذيرية إذا لم يتم تكوين البريد الإلكتروني
  static String get configurationWarning {
    if (!isConfigured) {
      return '''
⚠️ لم يتم تكوين إعدادات البريد الإلكتروني!

يرجى تحديث الملف: lib/config/email_config.dart

1. غير senderEmail إلى بريدك الإلكتروني
2. غير appPassword إلى كلمة مرور التطبيق من Gmail
3. اتبع التعليمات في الملف لإعداد Gmail

حالياً سيتم طباعة كود التحقق في وحدة التحكم للاختبار.
      ''';
    }
    return '';
  }
}
