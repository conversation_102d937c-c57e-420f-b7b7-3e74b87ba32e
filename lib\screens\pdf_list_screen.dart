import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../models/subject.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';
import '../services/pdf_service.dart';
import '../models/pdf_model.dart';
import 'pdf_viewer_screen.dart';

/// شاشة عرض قائمة ملفات PDF لمادة وقسم معين
class PDFListScreen extends StatefulWidget {
  final Subject subject;
  final String category;
  final List<String> pdfs; // للتوافق مع الكود القديم

  const PDFListScreen({
    super.key,
    required this.subject,
    required this.category,
    required this.pdfs,
  });

  @override
  State<PDFListScreen> createState() => _PDFListScreenState();
}

class _PDFListScreenState extends State<PDFListScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          appBar: AppBar(
            title: Text(
              '${widget.subject.arabicName} - ${widget.category}',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.w700,
                color: themeProvider.isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            backgroundColor:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            elevation: 0,
            iconTheme: IconThemeData(
              color: themeProvider.isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child:
                widget.pdfs.isNotEmpty
                    ? _buildDemoList()
                    : _buildEmptyStateWithDefaults(),
          ),
          floatingActionButton: _isAdmin() ? _buildAddButton() : null,
        );
      },
    );
  }

  /// بناء قائمة الملفات التجريبية
  Widget _buildDemoList() {
    // إذا لم توجد ملفات، عرض ملفات افتراضية
    if (widget.pdfs.isEmpty) {
      return _buildEmptyStateWithDefaults();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.pdfs.length,
      itemBuilder: (context, index) {
        final pdfName = widget.pdfs[index];
        return _buildDemoPDFCard(pdfName, index);
      },
    );
  }

  /// بناء حالة فارغة مع ملفات افتراضية
  Widget _buildEmptyStateWithDefaults() {
    final defaultPdfs = [
      'ملف_تجريبي_1.pdf',
      'ملف_تجريبي_2.pdf',
      'ملف_تجريبي_3.pdf',
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: defaultPdfs.length,
      itemBuilder: (context, index) {
        final pdfName = defaultPdfs[index];
        return _buildDemoPDFCard(pdfName, index);
      },
    );
  }

  /// بناء كارت PDF تجريبي
  Widget _buildDemoPDFCard(String pdfName, int index) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _openDemoPDF(pdfName),
              borderRadius: BorderRadius.circular(16),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF334155)
                            : const Color(0xFFE5E7EB),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  textDirection: TextDirection.rtl,
                  children: [
                    // أيقونة الملف
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF10B981).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.picture_as_pdf,
                        color: Color(0xFF10B981),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),

                    // معلومات الملف
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            pdfName.replaceAll('.pdf', ''),
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color:
                                  themeProvider.isDarkMode
                                      ? Colors.white
                                      : const Color(0xFF1F2937),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 14,
                                color: Colors.grey[500],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'جاهز للعرض',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Colors.grey[500],
                                ),
                              ),
                              const SizedBox(width: 12),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(
                                    0xFF10B981,
                                  ).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  'PDF',
                                  style: GoogleFonts.cairo(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF10B981),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // سهم التنقل
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFF64748B)
                              : const Color(0xFF9CA3AF),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء كارت PDF (غير مستخدمة حالياً)
  Widget _buildPDFCard(PDFModel pdf, int index) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _openPDF(pdf),
              borderRadius: BorderRadius.circular(16),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF334155)
                            : const Color(0xFFE5E7EB),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  textDirection: TextDirection.rtl,
                  children: [
                    // أيقونة الملف
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF10B981).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.picture_as_pdf,
                        color: Color(0xFF10B981),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),

                    // معلومات الملف
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            pdf.displayName,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color:
                                  themeProvider.isDarkMode
                                      ? Colors.white
                                      : const Color(0xFF1F2937),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 14,
                                color: Colors.grey[500],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                pdf.formattedDate,
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Colors.grey[500],
                                ),
                              ),
                              const SizedBox(width: 12),
                              Icon(
                                Icons.file_download,
                                size: 14,
                                color: Colors.grey[500],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${pdf.downloadCount}',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Colors.grey[500],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // سهم التنقل
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFF64748B)
                              : const Color(0xFF9CA3AF),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// زر إضافة ملف جديد
  Widget _buildAddButton() {
    return FloatingActionButton(
      onPressed: () {
        // هنا سيتم إضافة منطق إضافة ملف جديد
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'سيتم إضافة وظيفة إضافة الملفات قريباً',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: const Color(0xFF10B981),
          ),
        );
      },
      backgroundColor: const Color(0xFF10B981),
      child: const Icon(Icons.add, color: Colors.white),
    );
  }

  /// فتح ملف PDF
  void _openPDF(PDFModel pdf) {
    // زيادة عداد التحميل
    PDFService.incrementDownloadCount(pdf.id);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => PDFViewerScreen(
              subject: widget.subject,
              pdfFileName: pdf.displayName,
              category: widget.category,
              pdfUrl: pdf.url,
              pdfModel: pdf,
            ),
      ),
    );
  }

  /// فتح ملف PDF تجريبي
  void _openDemoPDF(String pdfName) {
    // رابط PDF تجريبي
    String pdfUrl =
        'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => PDFViewerScreen(
              subject: widget.subject,
              pdfFileName: pdfName,
              category: widget.category,
              pdfUrl: pdfUrl,
            ),
      ),
    );
  }

  /// التحقق من صلاحيات الأدمن
  bool _isAdmin() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.firebaseUser?.email == '<EMAIL>';
  }
}
