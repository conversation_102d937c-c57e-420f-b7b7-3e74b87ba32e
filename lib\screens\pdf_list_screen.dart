import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import '../models/subject.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';
import '../services/admin_service.dart';
import '../services/pdf_service.dart';
import '../models/pdf_model.dart';
import 'pdf_viewer_screen.dart';

class PDFListScreen extends StatefulWidget {
  final Subject subject;
  final String category;
  final List<String> pdfs; // للتوافق مع الكود القديم

  const PDFListScreen({
    super.key,
    required this.subject,
    required this.category,
    required this.pdfs,
  });

  @override
  State<PDFListScreen> createState() => _PDFListScreenState();
}

class _PDFListScreenState extends State<PDFListScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOutCubic,
      ),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// التحقق من صلاحيات الأدمن
  bool _isAdmin() {
    final authProvider = context.read<AuthProvider>();
    return authProvider.firebaseUser?.email == '<EMAIL>';
  }

  /// عرض خيارات الأدمن
  void _showAdminOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return Container(
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                ),
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // مؤشر السحب
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // عنوان
                    Text(
                      'إدارة ${widget.category} - ${widget.subject.arabicName}',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),

                    // خيارات الأدمن
                    _buildAdminOption(
                      icon: Icons.add_circle_outline,
                      title: 'إضافة ملف PDF',
                      subtitle: 'إضافة ملف جديد لهذا القسم',
                      color: const Color(0xFF10B981),
                      onTap: () {
                        Navigator.pop(context);
                        _navigateToAddPDF();
                      },
                      themeProvider: themeProvider,
                    ),
                    const SizedBox(height: 12),
                    const SizedBox(height: 20),
                  ],
                ),
              );
            },
          ),
    );
  }

  /// بناء خيار أدمن
  Widget _buildAdminOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    required ThemeProvider themeProvider,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF334155)
                      : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            textDirection: TextDirection.rtl,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                    ),
                    Text(
                      subtitle,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF64748B)
                        : const Color(0xFF9CA3AF),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// إضافة PDF مباشرة
  void _navigateToAddPDF() {
    _showAddPDFDialog();
  }

  /// عرض نافذة إضافة PDF
  void _showAddPDFDialog() {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController linkController = TextEditingController();
    String selectedFile = '';

    showDialog(
      context: context,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return StatefulBuilder(
                builder: (context, setState) {
                  return AlertDialog(
                    backgroundColor:
                        themeProvider.isDarkMode
                            ? const Color(0xFF1E293B)
                            : Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    title: Row(
                      textDirection: TextDirection.rtl,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(
                              0xFF10B981,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.add_circle_outline,
                            color: const Color(0xFF10B981),
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'إضافة ملف PDF جديد',
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.w700,
                              fontSize: 18,
                              color:
                                  themeProvider.isDarkMode
                                      ? Colors.white
                                      : const Color(0xFF1F2937),
                            ),
                          ),
                        ),
                      ],
                    ),
                    content: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // اسم الملف
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF334155)
                                      : const Color(0xFFF8FAFC),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: const Color(
                                  0xFF10B981,
                                ).withValues(alpha: 0.2),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  textDirection: TextDirection.rtl,
                                  children: [
                                    Icon(
                                      Icons.edit_outlined,
                                      color: const Color(0xFF10B981),
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'اسم الملف',
                                      style: GoogleFonts.cairo(
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF10B981),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                TextField(
                                  controller: nameController,
                                  style: GoogleFonts.cairo(
                                    color:
                                        themeProvider.isDarkMode
                                            ? Colors.white
                                            : const Color(0xFF1F2937),
                                  ),
                                  decoration: InputDecoration(
                                    hintText: 'مثال: أسئلة منتصف الترم',
                                    hintStyle: GoogleFonts.cairo(
                                      color:
                                          themeProvider.isDarkMode
                                              ? const Color(0xFF64748B)
                                              : const Color(0xFF9CA3AF),
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide.none,
                                    ),
                                    filled: true,
                                    fillColor:
                                        themeProvider.isDarkMode
                                            ? const Color(0xFF1E293B)
                                            : Colors.white,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),

                          // رابط الملف
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF334155)
                                      : const Color(0xFFF8FAFC),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: const Color(
                                  0xFF3B82F6,
                                ).withValues(alpha: 0.2),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  textDirection: TextDirection.rtl,
                                  children: [
                                    Icon(
                                      Icons.link_outlined,
                                      color: const Color(0xFF3B82F6),
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'رابط الملف',
                                      style: GoogleFonts.cairo(
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF3B82F6),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                TextField(
                                  controller: linkController,
                                  style: GoogleFonts.cairo(
                                    color:
                                        themeProvider.isDarkMode
                                            ? Colors.white
                                            : const Color(0xFF1F2937),
                                  ),
                                  decoration: InputDecoration(
                                    hintText: 'https://example.com/file.pdf',
                                    hintStyle: GoogleFonts.cairo(
                                      color:
                                          themeProvider.isDarkMode
                                              ? const Color(0xFF64748B)
                                              : const Color(0xFF9CA3AF),
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide.none,
                                    ),
                                    filled: true,
                                    fillColor:
                                        themeProvider.isDarkMode
                                            ? const Color(0xFF1E293B)
                                            : Colors.white,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),

                          // رفع ملف
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF334155)
                                      : const Color(0xFFF8FAFC),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: const Color(
                                  0xFF8B5CF6,
                                ).withValues(alpha: 0.2),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  textDirection: TextDirection.rtl,
                                  children: [
                                    Icon(
                                      Icons.upload_file_outlined,
                                      color: const Color(0xFF8B5CF6),
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'رفع ملف من الجهاز',
                                      style: GoogleFonts.cairo(
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF8B5CF6),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton.icon(
                                    onPressed: () async {
                                      await _pickPDFFile(setState, (fileName) {
                                        selectedFile = fileName;
                                      });
                                    },
                                    icon: Icon(
                                      selectedFile.isEmpty
                                          ? Icons.cloud_upload_outlined
                                          : Icons.check_circle_outline,
                                      size: 20,
                                    ),
                                    label: Text(
                                      selectedFile.isEmpty
                                          ? 'اختيار ملف PDF'
                                          : selectedFile,
                                      style: GoogleFonts.cairo(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          selectedFile.isEmpty
                                              ? const Color(0xFF8B5CF6)
                                              : const Color(0xFF10B981),
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 12,
                                        horizontal: 16,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          'إلغاء',
                          style: GoogleFonts.cairo(
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF94A3B8)
                                    : const Color(0xFF6B7280),
                          ),
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          if (nameController.text.trim().isNotEmpty &&
                              (linkController.text.trim().isNotEmpty ||
                                  selectedFile.isNotEmpty)) {
                            _addPDFToList(
                              nameController.text.trim(),
                              linkController.text.trim().isNotEmpty
                                  ? linkController.text.trim()
                                  : selectedFile,
                            );
                            Navigator.pop(context);
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'يرجى إدخال اسم الملف ورابط أو رفع ملف',
                                  style: GoogleFonts.cairo(),
                                ),
                                backgroundColor: const Color(0xFFEF4444),
                                behavior: SnackBarBehavior.floating,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF10B981),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                        child: Text(
                          'إضافة الملف',
                          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ],
                  );
                },
              );
            },
          ),
    );
  }

  /// اختيار ملف PDF من الجهاز
  Future<void> _pickPDFFile(
    StateSetter setState,
    Function(String) onFileSelected,
  ) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        final fileName = file.name;

        // التحقق من حجم الملف (أقل من 50 ميجا)
        final fileSizeInMB = file.size / (1024 * 1024);
        if (fileSizeInMB > 50) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'حجم الملف كبير جداً: ${fileSizeInMB.toStringAsFixed(1)} MB\nالحد الأقصى: 50 MB',
                  style: GoogleFonts.cairo(),
                ),
                backgroundColor: const Color(0xFFEF4444),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            );
          }
          return;
        }

        setState(() {
          onFileSelected(fileName);
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم اختيار الملف: $fileName\nالحجم: ${fileSizeInMB.toStringAsFixed(1)} MB',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: const Color(0xFF10B981),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في اختيار الملف: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: const Color(0xFFEF4444),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  /// إضافة PDF إلى القائمة مع رفع حقيقي
  Future<void> _addPDFToList(String name, String urlOrFileName) async {
    try {
      // التحقق من صلاحيات الأدمن
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (!_isAdmin()) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'ليس لديك صلاحية إضافة الملفات',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: const Color(0xFFEF4444),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
        return;
      }

      // عرض مؤشر التحميل
      if (mounted) {
        showDialog(
          context: context,

          builder:
              (context) => const Center(child: CircularProgressIndicator()),
        );
      }

      String finalUrl = urlOrFileName;
      double fileSize = 0.0;
      bool isFromUrl = true;
      String fileName = urlOrFileName;

      // إذا كان الملف مرفوع من الجهاز، نحتاج لرفعه إلى Firebase Storage
      if (!urlOrFileName.startsWith('http')) {
        // هنا سيتم إضافة منطق رفع الملف الحقيقي لاحقاً
        finalUrl = 'https://example.com/pdfs/$urlOrFileName';
        isFromUrl = false;
        fileName = urlOrFileName;
        fileSize = 1.5; // حجم تجريبي
      }

      // إضافة الملف إلى Firebase
      final success = await AdminService.addPDF(
        name: name,
        url: finalUrl,
        category: widget.category,
        subjectId: widget.subject.id,
        subjectName: widget.subject.arabicName,
        yearId: 'year1', // سيتم تحديثه لاحقاً
        semesterId: 'sem1', // سيتم تحديثه لاحقاً
        adminEmail: authProvider.firebaseUser?.email ?? '',
        adminName: authProvider.firebaseUser?.displayName ?? 'أدمن',
        fileSize: fileSize,
        fileName: fileName,
        isFromUrl: isFromUrl,
        originalUrl: isFromUrl ? urlOrFileName : null,
      );

      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل
      }

      if (success) {
        // إضافة الملف إلى القائمة المحلية
        setState(() {
          widget.pdfs.add(name);
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم إضافة الملف بنجاح: $name',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: const Color(0xFF10B981),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              action: SnackBarAction(
                label: 'عرض',
                textColor: Colors.white,
                onPressed: () {
                  _openPDF(name);
                },
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في إضافة الملف', style: GoogleFonts.cairo()),
              backgroundColor: const Color(0xFFEF4444),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل في حالة الخطأ

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة الملف: $e', style: GoogleFonts.cairo()),
            backgroundColor: const Color(0xFFEF4444),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  /// تعديل اسم PDF (محذوفة مؤقتاً)

  /// تعديل رابط PDF (محذوفة مؤقتاً)
  /*void _editPDFLink(String pdfName, int index) {
    final TextEditingController linkController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return AlertDialog(
                backgroundColor:
                    themeProvider.isDarkMode
                        ? const Color(0xFF1E293B)
                        : Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: Text(
                  'تعديل رابط الملف',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w700,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : const Color(0xFF1F2937),
                  ),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'أدخل الرابط الجديد للملف:',
                      style: GoogleFonts.cairo(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: linkController,
                      style: GoogleFonts.cairo(
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                      decoration: InputDecoration(
                        labelText: 'رابط الملف',
                        hintText: 'https://example.com/file.pdf',
                        labelStyle: GoogleFonts.cairo(),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor:
                            themeProvider.isDarkMode
                                ? const Color(0xFF334155)
                                : const Color(0xFFF8FAFC),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              // هنا يمكن إضافة منطق اختيار ملف جديد
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'سيتم إضافة وظيفة رفع الملفات قريباً',
                                    style: GoogleFonts.cairo(),
                                  ),
                                  backgroundColor: const Color(0xFF3B82F6),
                                  behavior: SnackBarBehavior.floating,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              );
                            },
                            icon: const Icon(Icons.upload_file, size: 18),
                            label: Text(
                              'رفع ملف',
                              style: GoogleFonts.cairo(fontSize: 12),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF10B981),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'إلغاء',
                      style: GoogleFonts.cairo(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      if (linkController.text.trim().isNotEmpty) {
                        // هنا يمكن إضافة منطق حفظ الرابط الجديد
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'تم تعديل رابط الملف بنجاح',
                              style: GoogleFonts.cairo(),
                            ),
                            backgroundColor: const Color(0xFF10B981),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        );
                        Navigator.pop(context);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF3B82F6),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'حفظ',
                      style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }

  /// حذف PDF
  void _deletePDF(String pdfName, int index) {
    showDialog(
      context: context,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return AlertDialog(
                backgroundColor:
                    themeProvider.isDarkMode
                        ? const Color(0xFF1E293B)
                        : Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: Text(
                  'تأكيد الحذف',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFFEF4444),
                  ),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.warning_amber_rounded,
                      color: const Color(0xFFEF4444),
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'هل أنت متأكد من حذف هذا الملف؟',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      pdfName,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFFEF4444).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color(0xFFEF4444).withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        'تحذير: لا يمكن التراجع عن هذا الإجراء',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: const Color(0xFFEF4444),
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'إلغاء',
                      style: GoogleFonts.cairo(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      // هنا يمكن إضافة منطق حذف الملف
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'تم حذف الملف: $pdfName',
                            style: GoogleFonts.cairo(),
                          ),
                          backgroundColor: const Color(0xFFEF4444),
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      );
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFEF4444),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'حذف',
                      style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }

  /// بناء خيار إدارة PDF
  Widget _buildPDFManagementOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    required ThemeProvider themeProvider,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF334155)
                      : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            textDirection: TextDirection.rtl,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                    ),
                    Text(
                      subtitle,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF64748B)
                        : const Color(0xFF9CA3AF),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء زر إضافة PDF للأدمن
  Widget _buildAddPDFButton() {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _navigateToAddPDF,
              borderRadius: BorderRadius.circular(16),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color(0xFF10B981).withValues(alpha: 0.3),
                    width: 2,
                    style: BorderStyle.solid,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(
                        alpha: themeProvider.isDarkMode ? 0.3 : 0.08,
                      ),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Row(
                  textDirection: TextDirection.rtl,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF10B981).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.add_circle_outline,
                        color: const Color(0xFF10B981),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'إضافة ملف PDF جديد',
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF10B981),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'اضغط لإضافة ملف جديد في قسم ${widget.category}',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF94A3B8)
                                      : const Color(0xFF6B7280),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getCategoryColor() {
    switch (widget.category) {
      case 'أسئلة':
        return const Color(0xFF3B82F6);
      case 'امتحانات':
        return const Color(0xFFEF4444);
      case 'ملخصات':
        return const Color(0xFF10B981);
      case 'الكتاب الرسمي':
        return const Color(0xFF8B5CF6);
      case 'أشهر المواضع':
        return const Color(0xFFFFB800);
      default:
        return const Color(0xFF10B981);
    }
  }

  IconData _getCategoryIcon() {
    switch (widget.category) {
      case 'أسئلة':
        return Icons.quiz_rounded;
      case 'امتحانات':
        return Icons.assignment_rounded;
      case 'ملخصات':
        return Icons.summarize_rounded;
      case 'الكتاب الرسمي':
        return Icons.menu_book_rounded;
      case 'أشهر المواضع':
        return Icons.star_rounded;
      default:
        return Icons.picture_as_pdf;
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoryColor = _getCategoryColor();

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: CustomScrollView(
              slivers: [
                // شريط التطبيق المخصص
                SliverAppBar(
                  expandedHeight: 200,
                  floating: false,
                  pinned: true,
                  backgroundColor: categoryColor,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            categoryColor,
                            categoryColor.withValues(alpha: 0.8),
                          ],
                        ),
                      ),
                      child: SafeArea(
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              // أيقونة الفئة
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 2,
                                  ),
                                ),
                                child: Icon(
                                  _getCategoryIcon(),
                                  color: Colors.white,
                                  size: 28,
                                ),
                              ),
                              const SizedBox(height: 16),

                              // عنوان الفئة
                              Text(
                                widget.category,
                                style: GoogleFonts.cairo(
                                  fontSize: 24,
                                  fontWeight: FontWeight.w700,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 4),

                              // اسم المادة
                              Text(
                                widget.subject.arabicName,
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  leading: IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.arrow_back_ios_rounded,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                  actions: [
                    if (_isAdmin())
                      Padding(
                        padding: const EdgeInsets.only(left: 16),
                        child: IconButton(
                          onPressed: _showAdminOptions,
                          icon: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.admin_panel_settings,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),

                // قائمة الملفات
                SliverPadding(
                  padding: const EdgeInsets.all(20),
                  sliver: StreamBuilder<List<PDFModel>>(
                    stream: PDFService.getPDFsStream(
                      subjectId: widget.subject.id,
                      category: widget.category,
                    ),
                    builder: (context, snapshot) {
                      if (snapshot.hasError) {
                        return SliverToBoxAdapter(
                          child: Center(
                            child: Column(
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  size: 64,
                                  color: Colors.red[300],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'خطأ في تحميل الملفات',
                                  style: GoogleFonts.cairo(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.red[300],
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  snapshot.error.toString(),
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    color: Colors.grey[600],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        );
                      }

                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return SliverToBoxAdapter(
                          child: Center(
                            child: Column(
                              children: [
                                const CircularProgressIndicator(),
                                const SizedBox(height: 16),
                                Text(
                                  'جاري تحميل الملفات...',
                                  style: GoogleFonts.cairo(
                                    fontSize: 16,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }

                      final pdfs = snapshot.data ?? [];

                      // إذا لم توجد ملفات
                      if (pdfs.isEmpty && !_isAdmin()) {
                        return SliverToBoxAdapter(child: _buildEmptyState());
                      }

                      return SliverList(
                        delegate: SliverChildBuilderDelegate((context, index) {
                          // إضافة زر الأدمن في النهاية
                          if (index == pdfs.length && _isAdmin()) {
                            return _buildAddPDFButton();
                          }

                          final pdf = pdfs[index];
                          return _buildPDFCardFromModel(pdf, index);
                        }, childCount: pdfs.length + (_isAdmin() ? 1 : 0)),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.folder_open_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 24),
          Text(
            'لا توجد ملفات في هذا القسم',
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إضافة الملفات قريباً',
            style: GoogleFonts.cairo(fontSize: 16, color: Colors.grey[500]),
          ),
          if (_isAdmin()) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _navigateToAddPDF,
              icon: const Icon(Icons.add),
              label: Text(
                'إضافة ملف جديد',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF10B981),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء كارت PDF من النموذج
  Widget _buildPDFCardFromModel(PDFModel pdf, int index) {
    final categoryColor = _getCategoryColor();

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _openPDFFromModel(pdf),
              onLongPress:
                  _isAdmin()
                      ? () => _showPDFOptionsFromModel(pdf, index)
                      : null,
              borderRadius: BorderRadius.circular(16),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF334155)
                            : const Color(0xFFE5E7EB),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  textDirection: TextDirection.rtl,
                  children: [
                    // أيقونة الملف
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: categoryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.picture_as_pdf,
                        color: categoryColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),

                    // معلومات الملف
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            pdf.displayName,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color:
                                  themeProvider.isDarkMode
                                      ? Colors.white
                                      : const Color(0xFF1F2937),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 14,
                                color: Colors.grey[500],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                pdf.formattedDate,
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Colors.grey[500],
                                ),
                              ),
                              const SizedBox(width: 12),
                              Icon(
                                Icons.file_download,
                                size: 14,
                                color: Colors.grey[500],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${pdf.downloadCount}',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Colors.grey[500],
                                ),
                              ),
                              if (pdf.fileSize > 0) ...[
                                const SizedBox(width: 12),
                                Icon(
                                  Icons.storage,
                                  size: 14,
                                  color: Colors.grey[500],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  pdf.formattedFileSize,
                                  style: GoogleFonts.cairo(
                                    fontSize: 12,
                                    color: Colors.grey[500],
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),

                    // سهم التنقل
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFF64748B)
                              : const Color(0xFF9CA3AF),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _openPDF(String pdfName) {
    // البحث عن الملف في قاعدة البيانات للحصول على الرابط الحقيقي
    // مؤقتاً سنستخدم رابط تجريبي
    String pdfUrl =
        'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => PDFViewerScreen(
              subject: widget.subject,
              pdfFileName: pdfName,
              category: widget.category,
              pdfUrl: pdfUrl,
            ),
      ),
    );
  }

  /// فتح ملف PDF من النموذج
  void _openPDFFromModel(PDFModel pdf) {
    // زيادة عداد التحميل
    PDFService.incrementDownloadCount(pdf.id);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => PDFViewerScreen(
              subject: widget.subject,
              pdfFileName: pdf.displayName,
              category: widget.category,
              pdfUrl: pdf.url,
              pdfModel: pdf,
            ),
      ),
    );
  }

  /// عرض خيارات PDF من النموذج
  void _showPDFOptionsFromModel(PDFModel pdf, int index) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return Container(
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                ),
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // مؤشر السحب
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // عنوان
                    Text(
                      'إدارة الملف',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      pdf.displayName,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),

                    // خيارات الإدارة
                    _buildPDFManagementOption(
                      icon: Icons.edit_outlined,
                      title: 'تعديل اسم الملف',
                      subtitle: 'تغيير اسم الملف المعروض',
                      color: const Color(0xFF3B82F6),
                      onTap: () {
                        Navigator.pop(context);
                        _editPDFNameFromModel(pdf);
                      },
                      themeProvider: themeProvider,
                    ),
                    const SizedBox(height: 12),
                    _buildPDFManagementOption(
                      icon: Icons.delete_outline,
                      title: 'حذف الملف',
                      subtitle: 'حذف الملف نهائياً من القسم',
                      color: const Color(0xFFEF4444),
                      onTap: () {
                        Navigator.pop(context);
                        _deletePDFFromModel(pdf);
                      },
                      themeProvider: themeProvider,
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              );
            },
          ),
    );
  }

  /// تعديل اسم PDF من النموذج
  void _editPDFNameFromModel(PDFModel pdf) {
    final TextEditingController nameController = TextEditingController(
      text: pdf.name,
    );

    showDialog(
      context: context,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return AlertDialog(
                backgroundColor:
                    themeProvider.isDarkMode
                        ? const Color(0xFF1E293B)
                        : Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: Text(
                  'تعديل اسم الملف',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w700,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : const Color(0xFF1F2937),
                  ),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'أدخل الاسم الجديد للملف:',
                      style: GoogleFonts.cairo(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: nameController,
                      style: GoogleFonts.cairo(
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                      decoration: InputDecoration(
                        labelText: 'اسم الملف',
                        labelStyle: GoogleFonts.cairo(),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor:
                            themeProvider.isDarkMode
                                ? const Color(0xFF334155)
                                : const Color(0xFFF8FAFC),
                      ),
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'إلغاء',
                      style: GoogleFonts.cairo(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      if (nameController.text.trim().isNotEmpty) {
                        final success = await PDFService.updatePDF(pdf.id, {
                          'name': nameController.text.trim(),
                        });

                        if (mounted) {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                success
                                    ? 'تم تعديل اسم الملف بنجاح'
                                    : 'فشل في تعديل اسم الملف',
                                style: GoogleFonts.cairo(),
                              ),
                              backgroundColor:
                                  success
                                      ? const Color(0xFF10B981)
                                      : const Color(0xFFEF4444),
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          );
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF3B82F6),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'حفظ',
                      style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }

  /// حذف PDF من النموذج
  void _deletePDFFromModel(PDFModel pdf) {
    showDialog(
      context: context,
      builder:
          (context) => Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return AlertDialog(
                backgroundColor:
                    themeProvider.isDarkMode
                        ? const Color(0xFF1E293B)
                        : Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: Text(
                  'تأكيد الحذف',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFFEF4444),
                  ),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.warning_amber_rounded,
                      color: const Color(0xFFEF4444),
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'هل أنت متأكد من حذف هذا الملف؟',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : const Color(0xFF1F2937),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      pdf.displayName,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFFEF4444).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color(0xFFEF4444).withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        'تحذير: لا يمكن التراجع عن هذا الإجراء',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: const Color(0xFFEF4444),
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'إلغاء',
                      style: GoogleFonts.cairo(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF94A3B8)
                                : const Color(0xFF6B7280),
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      final success = await PDFService.deletePDF(pdf.id);

                      if (mounted) {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              success
                                  ? 'تم حذف الملف بنجاح'
                                  : 'فشل في حذف الملف',
                              style: GoogleFonts.cairo(),
                            ),
                            backgroundColor:
                                success
                                    ? const Color(0xFF10B981)
                                    : const Color(0xFFEF4444),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFEF4444),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'حذف',
                      style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }
}
