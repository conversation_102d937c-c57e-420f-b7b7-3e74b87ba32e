import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/post_model.dart';
import '../models/chat_model.dart';
import '../services/firebase_service.dart';
import 'dart:io';

class FirebaseProvider extends ChangeNotifier {
  List<PostModel> _posts = [];
  List<ChatRoomModel> _chatRooms = [];
  bool _isLoading = false;
  String? _error;
  String _currentUserId = 'user_${DateTime.now().millisecondsSinceEpoch}';
  String _currentUserName = 'مستخدم';

  // Getters
  List<PostModel> get posts => _posts;
  List<ChatRoomModel> get chatRooms => _chatRooms;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get currentUserId => _currentUserId;
  String get currentUserName => _currentUserName;

  // Setters
  void setCurrentUser(String userId, String userName) {
    _currentUserId = userId;
    _currentUserName = userName;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// تحميل المنشورات
  void loadPosts() {
    FirebaseService.getPostsStream().listen(
      (QuerySnapshot snapshot) {
        _posts =
            snapshot.docs.map((doc) => PostModel.fromFirestore(doc)).toList();
        notifyListeners();
      },
      onError: (error) {
        _setError('خطأ في تحميل المنشورات: $error');
      },
    );
  }

  /// إضافة منشور جديد
  Future<bool> addPost({
    required String content,
    String? imageUrl,
    Map<String, dynamic>? pollData,
    List<String>? attachments,
    bool isAnonymous = false,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      await FirebaseService.addPost(
        content: content,
        authorName: _currentUserName,
        imageUrl: imageUrl,
        pollData: pollData,
        attachments: attachments,
        isAnonymous: isAnonymous,
      );

      _setLoading(false);
      return true;
    } catch (e) {
      _setError('خطأ في إضافة المنشور: $e');
      _setLoading(false);
      return false;
    }
  }

  /// إضافة إعجاب للمنشور
  Future<void> likePost(String postId) async {
    try {
      await FirebaseService.likePost(postId, _currentUserId);
    } catch (e) {
      _setError('خطأ في الإعجاب: $e');
    }
  }

  /// إضافة تعليق
  Future<void> addComment(String postId, String comment) async {
    try {
      await FirebaseService.addComment(postId, comment, _currentUserName);
    } catch (e) {
      _setError('خطأ في إضافة التعليق: $e');
    }
  }

  /// رفع صورة
  Future<String?> uploadImage(File imageFile) async {
    try {
      _setLoading(true);
      final fileName = 'images/${DateTime.now().millisecondsSinceEpoch}.jpg';
      final downloadUrl = await FirebaseService.uploadFile(imageFile, fileName);
      _setLoading(false);
      return downloadUrl;
    } catch (e) {
      _setError('خطأ في رفع الصورة: $e');
      _setLoading(false);
      return null;
    }
  }

  /// رفع ملف
  Future<String?> uploadFile(File file, String fileName) async {
    try {
      _setLoading(true);
      final filePath =
          'files/${DateTime.now().millisecondsSinceEpoch}_$fileName';
      final downloadUrl = await FirebaseService.uploadFile(file, filePath);
      _setLoading(false);
      return downloadUrl;
    } catch (e) {
      _setError('خطأ في رفع الملف: $e');
      _setLoading(false);
      return null;
    }
  }

  /// تحميل غرف المحادثة
  void loadChatRooms() {
    FirebaseService.getChatRoomsStream().listen(
      (QuerySnapshot snapshot) {
        _chatRooms =
            snapshot.docs
                .map((doc) => ChatRoomModel.fromFirestore(doc))
                .toList();
        notifyListeners();
      },
      onError: (error) {
        _setError('خطأ في تحميل غرف المحادثة: $error');
      },
    );
  }

  /// إنشاء غرفة محادثة جديدة
  Future<String?> createChatRoom({
    required String name,
    required String description,
    required String academicYear,
    bool isGeneral = false,
  }) async {
    try {
      _setLoading(true);
      _setError(null);

      final chatId = await FirebaseService.createChatRoom(
        name: name,
        description: description,
        academicYear: academicYear,
        isGeneral: isGeneral,
      );

      _setLoading(false);
      return chatId;
    } catch (e) {
      _setError('خطأ في إنشاء غرفة المحادثة: $e');
      _setLoading(false);
      return null;
    }
  }

  /// الانضمام إلى غرفة محادثة
  Future<bool> joinChatRoom(String chatId) async {
    try {
      await FirebaseService.joinChatRoom(chatId, _currentUserId);
      return true;
    } catch (e) {
      _setError('خطأ في الانضمام للمحادثة: $e');
      return false;
    }
  }

  /// مغادرة غرفة محادثة
  Future<bool> leaveChatRoom(String chatId) async {
    try {
      await FirebaseService.leaveChatRoom(chatId, _currentUserId);
      return true;
    } catch (e) {
      _setError('خطأ في مغادرة المحادثة: $e');
      return false;
    }
  }

  /// إرسال رسالة
  Future<bool> sendMessage(String chatId, String message) async {
    try {
      await FirebaseService.sendMessage(
        chatId: chatId,
        message: message,
        senderId: _currentUserId,
        senderName: _currentUserName,
      );
      return true;
    } catch (e) {
      _setError('خطأ في إرسال الرسالة: $e');
      return false;
    }
  }

  /// الحصول على رسائل المحادثة
  Stream<QuerySnapshot> getMessagesStream(String chatId) {
    return FirebaseService.getMessagesStream(chatId);
  }

  /// التحقق من عضوية المستخدم في غرفة المحادثة
  bool isUserMemberOfChat(String chatId) {
    final chatRoom = _chatRooms.firstWhere(
      (chat) => chat.id == chatId,
      orElse:
          () => ChatRoomModel(
            id: '',
            name: '',
            description: '',
            academicYear: '',
          ),
    );
    return chatRoom.isMember(_currentUserId);
  }

  /// الحصول على غرف المحادثة حسب السنة الدراسية
  List<ChatRoomModel> getChatRoomsByYear(String academicYear) {
    return _chatRooms
        .where((chat) => chat.academicYear == academicYear)
        .toList();
  }

  /// الحصول على المحادثة العامة
  ChatRoomModel? get generalChatRoom {
    try {
      return _chatRooms.firstWhere((chat) => chat.isGeneral);
    } catch (e) {
      return null;
    }
  }

  /// تصويت في استطلاع
  Future<bool> voteInPoll(String postId, int optionIndex) async {
    try {
      // هذه الوظيفة تحتاج إلى تنفيذ في FirebaseService
      // يمكنك إضافتها لاحقاً
      return true;
    } catch (e) {
      _setError('خطأ في التصويت: $e');
      return false;
    }
  }

  /// مسح الخطأ
  void clearError() {
    _setError(null);
  }

  /// تهيئة البيانات الأولية
  Future<void> initializeData() async {
    try {
      _setLoading(true);
      _setError(null);

      // print('🚀 بدء تهيئة Firebase...');

      // تحميل البيانات بشكل مبسط
      // إزالة التحميل الثقيل لتحسين الأداء

      // print('✅ تم إنهاء تهيئة Firebase بنجاح');
      _setLoading(false);
    } catch (e) {
      // print('❌ خطأ في تهيئة Firebase: $e');
      _setError('فشل في تهيئة البيانات: $e');
      _setLoading(false);
    }
  }

  // تم حذف الدوال غير المستخدمة _createDefaultChatRooms و _createDefaultPosts
}
