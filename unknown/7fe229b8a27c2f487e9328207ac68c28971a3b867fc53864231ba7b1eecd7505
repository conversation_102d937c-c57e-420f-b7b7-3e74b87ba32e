import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';

class FirebaseService {
  // Firebase instances
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;

  // Collections
  static const String postsCollection = 'posts';
  static const String chatsCollection = 'chats';
  static const String messagesCollection = 'messages';
  static const String usersCollection = 'users';

  /// الحصول على مرجع مجموعة المنشورات
  static CollectionReference get postsRef =>
      _firestore.collection(postsCollection);

  /// الحصول على مرجع مجموعة المحادثات
  static CollectionReference get chatsRef =>
      _firestore.collection(chatsCollection);

  /// الحصول على مرجع مجموعة المستخدمين
  static CollectionReference get usersRef =>
      _firestore.collection(usersCollection);

  /// إضافة منشور جديد
  static Future<String> addPost({
    required String content,
    required String authorName,
    String? imageUrl,
    Map<String, dynamic>? pollData,
    List<String>? attachments,
    bool isAnonymous = false,
  }) async {
    try {
      final docRef = await postsRef.add({
        'content': content,
        'authorName': isAnonymous ? 'مجهول' : authorName,
        'imageUrl': imageUrl,
        'pollData': pollData,
        'attachments': attachments ?? [],
        'isAnonymous': isAnonymous,
        'likes': 0,
        'likedBy': [],
        'comments': [],
        'shares': 0,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('Post added with ID: ${docRef.id}');
      }

      return docRef.id;
    } catch (e) {
      if (kDebugMode) {
        print('Error adding post: $e');
      }
      rethrow;
    }
  }

  /// الحصول على جميع المنشورات
  static Stream<QuerySnapshot> getPostsStream() {
    return postsRef.orderBy('createdAt', descending: true).snapshots();
  }

  /// إضافة إعجاب للمنشور
  static Future<void> likePost(String postId, String userId) async {
    try {
      final postRef = postsRef.doc(postId);

      await _firestore.runTransaction((transaction) async {
        final snapshot = await transaction.get(postRef);

        if (!snapshot.exists) {
          throw Exception('Post does not exist!');
        }

        final data = snapshot.data() as Map<String, dynamic>;
        final likedBy = List<String>.from(data['likedBy'] ?? []);
        final likes = data['likes'] ?? 0;

        if (likedBy.contains(userId)) {
          // إزالة الإعجاب
          likedBy.remove(userId);
          transaction.update(postRef, {'likes': likes - 1, 'likedBy': likedBy});
        } else {
          // إضافة الإعجاب
          likedBy.add(userId);
          transaction.update(postRef, {'likes': likes + 1, 'likedBy': likedBy});
        }
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error liking post: $e');
      }
      rethrow;
    }
  }

  /// إضافة تعليق للمنشور
  static Future<void> addComment(
    String postId,
    String comment,
    String authorName,
  ) async {
    try {
      final postRef = postsRef.doc(postId);

      await postRef.update({
        'comments': FieldValue.arrayUnion([
          {
            'id': DateTime.now().millisecondsSinceEpoch.toString(),
            'comment': comment,
            'authorName': authorName,
            'createdAt': FieldValue.serverTimestamp(),
          },
        ]),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error adding comment: $e');
      }
      rethrow;
    }
  }

  /// رفع ملف إلى Firebase Storage
  static Future<String> uploadFile(File file, String path) async {
    try {
      final ref = _storage.ref().child(path);
      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      if (kDebugMode) {
        print('File uploaded successfully: $downloadUrl');
      }

      return downloadUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading file: $e');
      }
      rethrow;
    }
  }

  /// إنشاء غرفة محادثة جديدة
  static Future<String> createChatRoom({
    required String name,
    required String description,
    required String academicYear,
    bool isGeneral = false,
  }) async {
    try {
      final docRef = await chatsRef.add({
        'name': name,
        'description': description,
        'academicYear': academicYear,
        'isGeneral': isGeneral,
        'members': [],
        'createdAt': FieldValue.serverTimestamp(),
        'lastMessage': null,
        'lastMessageTime': null,
      });

      if (kDebugMode) {
        print('Chat room created with ID: ${docRef.id}');
      }

      return docRef.id;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating chat room: $e');
      }
      rethrow;
    }
  }

  /// الانضمام إلى غرفة محادثة
  static Future<void> joinChatRoom(String chatId, String userId) async {
    try {
      await chatsRef.doc(chatId).update({
        'members': FieldValue.arrayUnion([userId]),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error joining chat room: $e');
      }
      rethrow;
    }
  }

  /// مغادرة غرفة محادثة
  static Future<void> leaveChatRoom(String chatId, String userId) async {
    try {
      await chatsRef.doc(chatId).update({
        'members': FieldValue.arrayRemove([userId]),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error leaving chat room: $e');
      }
      rethrow;
    }
  }

  /// إرسال رسالة في المحادثة
  static Future<void> sendMessage({
    required String chatId,
    required String message,
    required String senderId,
    required String senderName,
  }) async {
    try {
      final messagesRef = chatsRef.doc(chatId).collection(messagesCollection);

      await messagesRef.add({
        'message': message,
        'senderId': senderId,
        'senderName': senderName,
        'timestamp': FieldValue.serverTimestamp(),
        'type': 'text', // نوع الرسالة
        'imageUrl': null,
        'fileUrl': null,
        'fileName': null,
      });

      // تحديث آخر رسالة في غرفة المحادثة
      await chatsRef.doc(chatId).update({
        'lastMessage': message,
        'lastMessageTime': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error sending message: $e');
      }
      rethrow;
    }
  }

  /// الحصول على رسائل المحادثة - محسن للأداء
  static Stream<QuerySnapshot> getMessagesStream(String chatId) {
    return chatsRef
        .doc(chatId)
        .collection(messagesCollection)
        .orderBy('timestamp', descending: true)
        .limit(50) // تحديد عدد الرسائل لتحسين الأداء
        .snapshots();
  }

  /// الحصول على الرسائل مرة واحدة (للتحميل السريع)
  static Future<List<Map<String, dynamic>>> getMessagesOnce(
    String chatId,
  ) async {
    try {
      final snapshot =
          await chatsRef
              .doc(chatId)
              .collection(messagesCollection)
              .orderBy('timestamp', descending: true)
              .limit(50)
              .get();

      return snapshot.docs.map((doc) {
        final data = Map<String, dynamic>.from(doc.data());
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting messages: $e');
      }
      return [];
    }
  }

  /// الحصول على غرف المحادثة
  static Stream<QuerySnapshot> getChatRoomsStream() {
    return chatsRef.orderBy('createdAt', descending: false).snapshots();
  }

  /// الحصول على FCM Token
  static Future<String?> getFCMToken() async {
    try {
      return await _messaging.getToken();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting FCM token: $e');
      }
      return null;
    }
  }

  /// إرسال إشعار
  static Future<void> sendNotification({
    required String title,
    required String body,
    String? token,
    Map<String, dynamic>? data,
  }) async {
    // هذه الوظيفة تحتاج إلى تنفيذ من جانب الخادم
    // يمكنك استخدام Cloud Functions أو خادم منفصل
    if (kDebugMode) {
      print('Notification would be sent: $title - $body');
    }
  }
}
