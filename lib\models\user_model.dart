import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String email;
  final String displayName;
  final String? photoURL;
  final String? phoneNumber;
  final String academicYear;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final bool isEmailVerified;
  final String loginProvider; // google, apple, email, guest
  final Map<String, dynamic> preferences;

  const UserModel({
    required this.id,
    required this.email,
    required this.displayName,
    this.photoURL,
    this.phoneNumber,
    required this.academicYear,
    required this.createdAt,
    required this.lastLoginAt,
    required this.isEmailVerified,
    required this.loginProvider,
    this.preferences = const {},
  });

  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserModel(
      id: doc.id,
      email: data['email'] ?? '',
      displayName: data['displayName'] ?? '',
      photoURL: data['photoURL'],
      phoneNumber: data['phoneNumber'],
      academicYear: data['academicYear'] ?? 'السنة الأولى',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastLoginAt: (data['lastLoginAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isEmailVerified: data['isEmailVerified'] ?? false,
      loginProvider: data['loginProvider'] ?? 'email',
      preferences: Map<String, dynamic>.from(data['preferences'] ?? {}),
    );
  }

  factory UserModel.fromMap(Map<String, dynamic> data) {
    return UserModel(
      id: data['id'] ?? '',
      email: data['email'] ?? '',
      displayName: data['displayName'] ?? '',
      photoURL: data['photoURL'],
      phoneNumber: data['phoneNumber'],
      academicYear: data['academicYear'] ?? 'السنة الأولى',
      createdAt: data['createdAt'] is Timestamp 
          ? (data['createdAt'] as Timestamp).toDate()
          : data['createdAt'] is DateTime 
              ? data['createdAt'] as DateTime
              : DateTime.now(),
      lastLoginAt: data['lastLoginAt'] is Timestamp 
          ? (data['lastLoginAt'] as Timestamp).toDate()
          : data['lastLoginAt'] is DateTime 
              ? data['lastLoginAt'] as DateTime
              : DateTime.now(),
      isEmailVerified: data['isEmailVerified'] ?? false,
      loginProvider: data['loginProvider'] ?? 'email',
      preferences: Map<String, dynamic>.from(data['preferences'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'photoURL': photoURL,
      'phoneNumber': phoneNumber,
      'academicYear': academicYear,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastLoginAt': Timestamp.fromDate(lastLoginAt),
      'isEmailVerified': isEmailVerified,
      'loginProvider': loginProvider,
      'preferences': preferences,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoURL,
    String? phoneNumber,
    String? academicYear,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isEmailVerified,
    String? loginProvider,
    Map<String, dynamic>? preferences,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      academicYear: academicYear ?? this.academicYear,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      loginProvider: loginProvider ?? this.loginProvider,
      preferences: preferences ?? this.preferences,
    );
  }

  // دوال مساعدة
  String get firstName => displayName.split(' ').first;
  String get initials {
    final names = displayName.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}';
    }
    return displayName.isNotEmpty ? displayName[0] : 'U';
  }

  bool get isGuest => loginProvider == 'guest';
  bool get isGoogleUser => loginProvider == 'google';
  bool get isAppleUser => loginProvider == 'apple';
  bool get isEmailUser => loginProvider == 'email';

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, displayName: $displayName, loginProvider: $loginProvider)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
