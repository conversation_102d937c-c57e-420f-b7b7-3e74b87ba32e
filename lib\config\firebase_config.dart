import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

class FirebaseConfig {
  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD__fwZYKuehthlJoqGKSDt0YN1TCsPUY8',
    appId: '1:801031214670:android:a179401f6b476d34db551f',
    messagingSenderId: '801031214670',
    projectId: 'legal2025',
    storageBucket: 'legal2025.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyD__fwZYKuehthlJoqGKSDt0YN1TCsPUY8',
    appId: '1:801031214670:ios:a179401f6b476d34db551f',
    messagingSenderId: '801031214670',
    projectId: 'legal2025',
    storageBucket: 'legal2025.firebasestorage.app',
    iosBundleId: 'com.legal2025.yamy',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyD__fwZYKuehthlJoqGKSDt0YN1TCsPUY8',
    appId: '1:801031214670:web:a179401f6b476d34db551f',
    messagingSenderId: '801031214670',
    projectId: 'legal2025',
    authDomain: 'legal2025.firebaseapp.com',
    storageBucket: 'legal2025.firebasestorage.app',
  );

  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return ios;
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for this platform.',
        );
    }
  }

  /// تهيئة Firebase
  static Future<void> initialize() async {
    await Firebase.initializeApp(options: currentPlatform);

    // تهيئة Firebase Messaging
    await _initializeMessaging();
  }

  /// تهيئة خدمة الإشعارات
  static Future<void> _initializeMessaging() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    // طلب أذونات الإشعارات
    await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    // تم منح الأذونات

    // الحصول على FCM token
    await messaging.getToken();
    // تم الحصول على FCM token

    // معالجة الإشعارات في الخلفية
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // معالجة الإشعارات عندما يكون التطبيق مفتوحاً
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      if (kDebugMode) {
        print('Got a message whilst in the foreground!');
        print('Message data: ${message.data}');
      }

      if (message.notification != null) {
        if (kDebugMode) {
          print(
            'Message also contained a notification: ${message.notification}',
          );
        }
      }
    });

    // معالجة النقر على الإشعارات
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      if (kDebugMode) {
        print('A new onMessageOpenedApp event was published!');
      }
      // يمكنك إضافة منطق التنقل هنا
    });
  }
}

/// معالج الإشعارات في الخلفية
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: FirebaseConfig.currentPlatform);

  // معالجة رسالة في الخلفية
}
