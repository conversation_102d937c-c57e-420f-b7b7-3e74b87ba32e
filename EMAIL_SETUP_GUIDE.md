# 📧 دليل إعداد البريد الإلكتروني - Legal 2025

## 🎯 **نظرة عامة**

هذا الدليل يوضح كيفية إعداد إرسال البريد الإلكتروني لأكواد التحقق في تطبيق Legal 2025.

## ⚠️ **المشكلة الحالية**

حالياً، النظام لا يرسل أكواد التحقق إلى البريد الإلكتروني الفعلي لأن إعدادات البريد الإلكتروني غير مُكونة. بدلاً من ذلك، يتم طباعة الكود في وحدة التحكم للاختبار.

## 🔧 **الحل: إعداد Gmail لإرسال البريد الإلكتروني**

### **الخطوة 1: إعداد حساب Gmail**

1. **إنشاء حساب Gmail جديد** (أو استخدام حساب موجود):
   - اذهب إلى: https://accounts.google.com/signup
   - أنشئ حساب جديد مخصص للتطبيق (مثل: <EMAIL>)

### **الخطوة 2: تفعيل التحقق بخطوتين**

1. **اذهب إلى إعدادات الأمان**:
   - زر الرابط: https://myaccount.google.com/security

2. **تفعيل التحقق بخطوتين**:
   - ابحث عن "2-Step Verification"
   - اضغط "Get started"
   - اتبع التعليمات لتفعيل التحقق بخطوتين

### **الخطوة 3: إنشاء كلمة مرور التطبيق**

1. **بعد تفعيل التحقق بخطوتين**:
   - في نفس صفحة الأمان، ابحث عن "App passwords"
   - اضغط على "App passwords"

2. **إنشاء كلمة مرور جديدة**:
   - اختر "Mail" كنوع التطبيق
   - اختر "Other (Custom name)" واكتب "Legal 2025 App"
   - اضغط "Generate"

3. **نسخ كلمة المرور**:
   - انسخ كلمة المرور المُنشأة (16 حرف)
   - ⚠️ **مهم**: احفظها في مكان آمن، لن تظهر مرة أخرى!

### **الخطوة 4: تحديث إعدادات التطبيق**

1. **افتح الملف**: `lib/config/email_config.dart`

2. **حدث الإعدادات**:
```dart
class EmailConfig {
  // غير هذا إلى بريدك الإلكتروني
  static const String senderEmail = '<EMAIL>';
  static const String senderName = 'تطبيق Legal 2025';
  
  // غير هذا إلى كلمة مرور التطبيق المُنشأة
  static const String appPassword = 'your_16_character_app_password';
}
```

3. **مثال على الإعدادات الصحيحة**:
```dart
class EmailConfig {
  static const String senderEmail = '<EMAIL>';
  static const String senderName = 'تطبيق Legal 2025';
  static const String appPassword = 'abcd efgh ijkl mnop'; // كلمة مرور التطبيق الحقيقية
}
```

## 🧪 **اختبار الإعداد**

### **1. تشغيل التطبيق**
```bash
flutter run
```

### **2. اختبار إرسال كود التحقق**
1. اذهب إلى شاشة إنشاء حساب جديد
2. أدخل بريد إلكتروني صحيح
3. اضغط "إرسال كود التحقق"
4. تحقق من وحدة التحكم للرسائل

### **3. رسائل النجاح المتوقعة**
```
✅ تم إرسال البريد الإلكتروني بنجاح إلى <EMAIL>
📧 تفاصيل الإرسال: ...
```

### **4. رسائل الخطأ المحتملة**
```
❌ خطأ في إرسال البريد الإلكتروني: ...
🔧 يبدو أن هناك مشكلة في إعدادات البريد الإلكتروني
```

## 🚨 **حل المشاكل الشائعة**

### **مشكلة: "Authentication failed"**
**السبب**: كلمة مرور التطبيق غير صحيحة
**الحل**: 
- تأكد من استخدام كلمة مرور التطبيق وليس كلمة مرور الحساب العادية
- أنشئ كلمة مرور تطبيق جديدة

### **مشكلة: "Less secure app access"**
**السبب**: Gmail يرفض الاتصال
**الحل**: 
- تأكد من تفعيل التحقق بخطوتين
- استخدم كلمة مرور التطبيق

### **مشكلة: "Invalid email address"**
**السبب**: البريد الإلكتروني غير صحيح
**الحل**: 
- تأكد من صحة البريد الإلكتروني في EmailConfig
- تأكد من عدم وجود مسافات إضافية

## 🔒 **الأمان**

### **⚠️ تحذيرات مهمة**
1. **لا تشارك كلمة مرور التطبيق** مع أحد
2. **لا ترفع كلمة المرور إلى Git** أو أي مستودع عام
3. **استخدم حساب Gmail منفصل** للتطبيق
4. **في الإنتاج، استخدم متغيرات البيئة** لحفظ كلمة المرور

### **🛡️ أفضل الممارسات**
```dart
// ❌ خطأ - كلمة المرور في الكود
static const String appPassword = 'abcd efgh ijkl mnop';

// ✅ صحيح - استخدام متغيرات البيئة
static String get appPassword => 
    const String.fromEnvironment('EMAIL_APP_PASSWORD');
```

## 🚀 **بدائل أخرى**

إذا لم تتمكن من إعداد Gmail، يمكنك استخدام:

### **1. SendGrid (مُوصى به)**
- مجاني حتى 100 بريد يومياً
- سهل الإعداد
- موثوق وسريع

### **2. Firebase Functions**
- متكامل مع Firebase
- آمن ومُدار بالكامل
- يتطلب خطة Blaze

### **3. خدمات أخرى**
- Amazon SES
- Mailgun
- Twilio SendGrid
- Postmark

## 📝 **ملاحظات إضافية**

### **للاختبار فقط**
حالياً، إذا فشل إرسال البريد الإلكتروني، سيتم طباعة كود التحقق في وحدة التحكم للاختبار.

### **في الإنتاج**
يجب إزالة طباعة كود التحقق في وحدة التحكم لأسباب أمنية.

### **تحسينات مستقبلية**
- إضافة قوالب بريد إلكتروني متعددة
- دعم لغات متعددة
- إحصائيات إرسال البريد الإلكتروني
- إعادة المحاولة التلقائية

## 🆘 **الدعم**

إذا واجهت أي مشاكل:

1. **تحقق من وحدة التحكم** للرسائل التفصيلية
2. **تأكد من اتباع جميع الخطوات** بالترتيب
3. **جرب إنشاء كلمة مرور تطبيق جديدة**
4. **تأكد من الاتصال بالإنترنت**

---

**✅ بعد إكمال هذه الخطوات، سيتم إرسال أكواد التحقق إلى البريد الإلكتروني الفعلي!**
