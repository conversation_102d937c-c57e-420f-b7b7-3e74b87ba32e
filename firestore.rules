rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح بالقراءة والكتابة للمستخدمين المصادق عليهم
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // قواعد للأدمن
    match /admins/{adminId} {
      allow read, write: if request.auth != null &&
        (request.auth.token.email == '<EMAIL>' ||
         resource.data.email == request.auth.token.email);
    }

    // قواعد ملفات PDF - مؤقتاً للاختبار
    match /pdfs/{pdfId} {
      allow read: if true; // يمكن للجميع قراءة ملفات PDF
      allow write: if request.auth != null; // مؤقتاً - أي مستخدم مسجل يمكنه الكتابة
    }

    // قواعد الإشعارات - مؤقتاً للاختبار
    match /notifications/{notificationId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null; // مؤقتاً - أي مستخدم مسجل يمكنه الكتابة
    }

    // السماح بالقراءة والكتابة للمنشورات
    match /posts/{postId} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد التعليقات
    match /comments/{commentId} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد الإعجابات
    match /likes/{likeId} {
      allow read, write: if request.auth != null;
    }

    // قواعد المشاركات
    match /shares/{shareId} {
      allow read, write: if request.auth != null;
    }

    // قواعد الاستطلاعات
    match /polls/{pollId} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد أصوات الاستطلاعات
    match /poll_votes/{voteId} {
      allow read, write: if request.auth != null;
    }

    // السماح بالقراءة والكتابة للرسائل
    match /chats/{chatId} {
      allow read, write: if request.auth != null;
    }

    // قواعد رسائل الدردشة
    match /chat_messages/{messageId} {
      allow read, write: if request.auth != null;
    }

    // السماح بالقراءة والكتابة للتسجيلات المؤقتة
    match /pending_registrations/{email} {
      allow read, write: if true;
    }
  }
}
