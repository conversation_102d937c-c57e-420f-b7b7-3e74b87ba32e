import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;

/// خدمة بريد إلكتروني مبسطة للاختبار
class SimpleEmailService {
  // إعدادات خدمة EmailJS المجانية
  static const String _serviceId = 'service_legal2025';
  static const String _templateId = 'template_verification';
  static const String _publicKey = 'legal2025_public_key';

  /// إرسال كود التحقق عبر البريد الإلكتروني
  static Future<bool> sendVerificationCode(String email, String code) async {
    try {
      // محاولة الإرسال الحقيقي أولاً
      final realEmailSent = await _sendRealEmail(email, code);
      if (realEmailSent) {
        print('✅ تم إرسال البريد الإلكتروني بنجاح إلى: $email');
        return true;
      }

      // إذا فشل الإرسال الحقيقي، استخدم النظام البديل
      return await _sendMockEmail(email, code);
    } catch (e) {
      print('❌ خطأ في إرسال البريد الإلكتروني: $e');
      return await _sendMockEmail(email, code);
    }
  }

  /// محاولة إرسال بريد إلكتروني حقيقي
  static Future<bool> _sendRealEmail(String email, String code) async {
    try {
      // استخدام خدمة EmailJS المجانية
      final response = await http.post(
        Uri.parse('https://api.emailjs.com/api/v1.0/email/send'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'service_id': _serviceId,
          'template_id': _templateId,
          'user_id': _publicKey,
          'template_params': {
            'to_email': email,
            'verification_code': code,
            'app_name': 'Legal 2025',
            'message': 'كود التحقق الخاص بك هو: $code',
          },
        }),
      );

      if (response.statusCode == 200) {
        print('✅ تم إرسال البريد الإلكتروني عبر EmailJS');
        return true;
      } else {
        print('❌ فشل إرسال البريد عبر EmailJS: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('❌ خطأ في EmailJS: $e');
      return false;
    }
  }

  /// نظام بديل لمحاكاة إرسال البريد الإلكتروني
  static Future<bool> _sendMockEmail(String email, String code) async {
    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 2));

      // طباعة تفاصيل البريد الإلكتروني المُرسل
      print('📧 ═══════════════════════════════════════');
      print('📧 محاكاة إرسال بريد إلكتروني');
      print('📧 ═══════════════════════════════════════');
      print('📧 إلى: $email');
      print('📧 الموضوع: كود التحقق - تطبيق Legal 2025');
      print('📧 ═══════════════════════════════════════');
      print('📧 محتوى الرسالة:');
      print('📧');
      print('📧 مرحباً!');
      print('📧');
      print('📧 كود التحقق الخاص بك هو:');
      print('📧');
      print('📧 ┌─────────────┐');
      print('📧 │    $code    │');
      print('📧 └─────────────┘');
      print('📧');
      print('📧 هذا الكود صالح لمدة 10 دقائق فقط.');
      print('📧');
      print('📧 تطبيق Legal 2025');
      print('📧 ═══════════════════════════════════════');

      // محاكاة نجاح الإرسال (دائماً ناجح للاختبار)
      print('✅ تم إرسال البريد الإلكتروني بنجاح (محاكاة)');

      // إشعار للمطور
      print('');
      print('🔔 ملاحظة للمطور:');
      print('   هذا نظام محاكاة للاختبار.');
      print('   لإرسال بريد إلكتروني حقيقي، يرجى:');
      print('   1. إعداد حساب Gmail في EmailConfig');
      print('   2. أو استخدام خدمة EmailJS');
      print('   3. أو تكوين خدمة بريد إلكتروني أخرى');
      print('');

      return true;
    } catch (e) {
      print('❌ خطأ في نظام المحاكاة: $e');
      return false;
    }
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// إنشاء كود تحقق عشوائي
  static String generateVerificationCode() {
    final random = Random();
    String code = '';
    for (int i = 0; i < 6; i++) {
      code += random.nextInt(10).toString();
    }
    return code;
  }
}
